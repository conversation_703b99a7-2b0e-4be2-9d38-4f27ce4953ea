<?php

namespace App\Http\Client;

use Illuminate\Support\Facades\Http;

class OpenFoodFactsClient
{
    public function searchProduct(string $barcode): ?array
    {
        $response = Http::get("https://cl.openfoodfacts.net/api/v2/product/$barcode");

        if ($response->successful()) {
            $productData = $response->json();

            if (isset($productData['product'])) {
                return $this->mapProductData($productData['product']);
            }
        }

        $response->throw();
        // broadcast(new ())
        return null;
    }

    private function mapProductData(array $productData)
    {
        return [
            'name' => $productData['product_name_es'] ?? $productData['product_name'],
            'description' => $productData['generic_name'] ?? $productData['product_name'].' '.$productData['brands'],
            'barcode' => $productData['code'],
            'image' => $productData['image_url'],
            'category_id' => 1,
            'expiration_date' => null,
        ];
    }
}
