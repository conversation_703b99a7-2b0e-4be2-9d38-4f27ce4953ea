<?php

namespace App\Http\Controllers\StockMovement;

use App\Commands\StockEntryCommand;
use App\Dtos\StockEntryDto;
use App\Http\Controllers\Controller;
use App\Http\Requests\StockEntryRequest;
use App\Interfaces\services\IStockMovementService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class StockMovementController extends Controller
{
    private StockEntryCommand $stockEntryCommand;

    public function __construct(StockEntryCommand $stockEntryCommand)
    {
        $this->stockEntryCommand = $stockEntryCommand;
    }

    public function index()
    {
        $locals = Auth::user()->locals;

        return Inertia::render('stockMovement/StockMovement', ['locals' => $locals]);
    }

     /**
     * Handle stock entry request
     */
    public function store(StockEntryRequest $request)
    {
        try {
            // Create DTO from validated request data
            $stockEntryDto = new StockEntryDto(
                product_id: $request->validated('product_id'),
                local_id: $request->validated('local_id'),
                quantity: $request->validated('quantity'),
                movement_reason: $request->validated('movement_reason'),
                batch_id: $request->validated('batch_id'),
                user_id: Auth::id(),
                movement_type: 'Ingreso de Stock'
            );

            // Execute the stock entry command
            $result = $this->stockEntryCommand->execute($stockEntryDto);

            // Log successful operation
            Log::info('Stock entry completed via API', [
                'user_id' => Auth::id(),
                'product_id' => $stockEntryDto->product_id,
                'local_id' => $stockEntryDto->local_id,
                'quantity' => $stockEntryDto->quantity,
                'stock_movement_id' => $result['data']['stock_movement']->id ?? null,
            ]);

            return response()->json($result, 200);

        } catch (\Exception $e) {
            Log::error('Stock entry failed', [
                'user_id' => Auth::id(),
                'request_data' => $request->validated(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error' => 'STOCK_ENTRY_FAILED'
            ], 422);
        }
    }
}
