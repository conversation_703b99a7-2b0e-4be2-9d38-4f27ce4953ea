<?php

namespace App\Http\Controllers\Batch;

use App\Http\Controllers\Controller;
use App\Models\Batch;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BatchController extends Controller
{
    public function createBatchAtStockEntry(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'barcode' => 'nullable|unique:batches,barcode',
            'expiration_date' => 'nullable|date',
            'quantity' => 'required|integer|min:0',
        ]);

        $batch = Batch::create($validated);

        return Inertia::render('stockMovement/StockMovement', ['batch' => $batch]);
    }
}
