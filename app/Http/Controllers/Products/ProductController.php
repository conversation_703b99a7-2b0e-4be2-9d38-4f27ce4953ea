<?php

namespace App\Http\Controllers\Products;

use App\Dtos\CreateProductDto;
use App\Http\Controllers\Controller;
use App\Http\Requests\Product\StoreProductRequest;
use App\Interfaces\services\IProductService;
use App\Models\Category;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;

class ProductController extends Controller
{
    private IProductService $productService;

    public function __construct(IProductService $productService)
    {
        $this->productService = $productService;
    }

    public function index()
    {
        $local = Auth::user()->locals->first();
        $productName = Request::get('productName');
        $categoryId = Request::get('categoryId');

        $products = $this->productService->getFilteredProducts(
            $local,
            $productName,
            $categoryId
        );

        $categories = Category::all();

        return Inertia::render('InventoryManagement', [
            'locals' => Auth::user()->locals,
            'products' => $products->paginate(5),
            'categories' => $categories,
        ]);
    }

    public function findByBarcode($barcode)
    {
        $localProduct = $this->productService->findByBarcode($barcode);

        if (!$localProduct) {
            return response()->json([
                'error' => true,
                'message' => 'Producto No encontrado en nuestra base de datos local. Buscando en base de datos externa...',
            ], 404);
        }

        return response()->json($localProduct);
    }

    public function createProduct(StoreProductRequest $request)
    {
        // dd($request->validated());
        $productDto = new CreateProductDto(...$request->validated());

        $product = $this->productService->createProduct($productDto);

        return redirect()->back()->with('product', $product->load(['category']));
    }
}
