<?php

namespace App\Http\Controllers\LocalProduct;

use App\Dtos\CreateLocalProductDto;
use App\Http\Controllers\Controller;
use App\Interfaces\services\ILocalProductService;
use App\Models\LocalProduct;
use App\Models\Product;
use Illuminate\Http\Request;

class LocalProductController extends Controller
{
    private ILocalProductService $localProductService;

    public function __construct(ILocalProductService $localProductService)
    {
        $this->localProductService = $localProductService;
    }

    public function addProductToLocal(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'local_id' => 'required|exists:locals,id',
        ]);

        // Check if product already exists in this local
        $existingLocalProduct = LocalProduct::where('product_id', $validated['product_id'])
            ->where('local_id', $validated['local_id'])
            ->first();

        if ($existingLocalProduct) {
            return response()->json([
                'error' => 'El producto ya existe en este local',
            ], 422);
        }

        // Get the product to use its data
        $product = Product::find($validated['product_id']);

        // Create local product with minimal required data and 0 stock
        $localProductDto = new CreateLocalProductDto(
            local_id: $validated['local_id'],
            product_id: $validated['product_id'],
            stock: 0, // Start with 0 stock
            price: 0, // Default price, can be updated later
            min_stock: 0, // Default min stock
            max_stock: 100, // Default max stock
            expiration_date: $product->expiration_date ?? now()->addYear()->format('Y-m-d H:i:s'),
            status: true // Set status to active
        );

        $localProduct = $this->localProductService->createLocalProduct($localProductDto);

        // Return the product with updated local_product information
        $productWithLocalData = Product::with(['category', 'batches', 'stock' => function ($query) use ($validated) {
            $query->where('local_id', $validated['local_id']);
        }])->find($validated['product_id']);

        return response()->json([
            'success' => true,
            'message' => 'Producto agregado al local correctamente',
            'product' => $productWithLocalData,
        ]);
    }
}
