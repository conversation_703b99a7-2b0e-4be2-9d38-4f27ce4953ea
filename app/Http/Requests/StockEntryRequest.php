<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StockEntryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // return auth()->check();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => [
                'required',
                'integer',
                'exists:products,id'
            ],
            'local_id' => [
                'required',
                'integer',
                'exists:locals,id',
                // Validate that the product exists in this local
                Rule::exists('local_products', 'local_id')->where(function ($query) {
                    return $query->where('product_id', $this->input('product_id'));
                }),
            ],
            'quantity' => [
                'required',
                'integer',
                'min:1',
                'max:999999'
            ],
            'movement_reason' => [
                'required',
                'string',
                'max:500',
                'min:3'
            ],
            'batch_id' => [
                'nullable',
                'integer',
                'exists:batches,id',
                // Validate that the batch belongs to the product
                Rule::exists('batches', 'id')->where(function ($query) {
                    return $query->where('product_id', $this->input('product_id'));
                }),
            ],
        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'product_id.required' => 'El producto es obligatorio.',
            'product_id.exists' => 'El producto seleccionado no existe.',
            'local_id.required' => 'El local es obligatorio.',
            'local_id.exists' => 'El local seleccionado no existe o el producto no está disponible en este local.',
            'quantity.required' => 'La cantidad es obligatoria.',
            'quantity.min' => 'La cantidad debe ser mayor a 0.',
            'quantity.max' => 'La cantidad no puede ser mayor a 999,999.',
            'movement_reason.required' => 'El motivo del movimiento es obligatorio.',
            'movement_reason.min' => 'El motivo debe tener al menos 3 caracteres.',
            'movement_reason.max' => 'El motivo no puede exceder 500 caracteres.',
            'batch_id.exists' => 'El lote seleccionado no existe o no pertenece al producto.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'product_id' => 'producto',
            'local_id' => 'local',
            'quantity' => 'cantidad',
            'movement_reason' => 'motivo del movimiento',
            'batch_id' => 'lote',
        ];
    }
}
