<?php

namespace App\Http\Requests\Product;

use Illuminate\Foundation\Http\FormRequest;

class StoreProductRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'description' => 'required|string|max:500',
            'barcode' => 'required|unique:products|string|min:13',
            'image' => 'nullable|string|max:100',
            'price' => 'required|integer|min:0',
            'category_id' => 'required|exists:categories,id',
            'expiration_date' => 'nullable|date',
            'initial_stock' => 'nullable|integer|min:0',
            'batch_id' => 'nullable|exists:batches,id',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => 'El nombre es obligatorio.',
            'name.string' => 'El nombre debe ser una cadena de texto.',
            'name.max' => 'El nombre no puede tener más de 100 caracteres.',
            'barcode.required' => 'El código de barras es obligatorio.',
            'barcode.string' => 'El código de barras debe ser una cadena de texto.',
            'barcode.min' => 'El código de barras no puede tener menos de 13 caracteres.',
            'barcode.unique' => 'El código de barras ya existe, verifica si ya fue registrado este producto.',
            'description.required' => 'La descripción es obligatoria.',
            'description.string' => 'La descripción debe ser una cadena de texto.',
            'description.max' => 'La descripción no puede tener más de 500 caracteres.',
            'image.string' => 'La imagen debe ser una cadena de texto.',
            'price.required' => 'El precio es obligatorio.',
            'price.integer' => 'El precio debe ser un número entero.',
            'price.min' => 'El precio no puede ser menor que 0.',
            'category.required' => 'La categoría es obligatoria.',
            'category.exists' => 'La categoría seleccionada no es válida.',
            'expiration_date.date' => 'La fecha de caducidad debe ser una fecha válida.',
            'initial_stock.integer' => 'El stock inicial debe ser un número entero.',
            'initial_stock.min' => 'El stock inicial no puede ser menor que 0.',
            'batch_id.exists' => 'El lote seleccionado no es válido.',
        ];
    }
}
