<?php

namespace App\Dtos;

class StockEntryDto
{
    public function __construct(
        public readonly int $product_id,
        public readonly int $local_id,
        public readonly int $quantity,
        public readonly string $movement_reason,
        public readonly ?int $batch_id = null,
        public readonly ?int $user_id = null,
        public readonly string $movement_type = 'Ingreso de Stock'
    ) {
    }

    public function toArray(): array
    {
        return [
            'product_id' => $this->product_id,
            'local_id' => $this->local_id,
            'quantity' => $this->quantity,
            'movement_reason' => $this->movement_reason,
            'batch_id' => $this->batch_id,
            'user_id' => $this->user_id,
            'movement_type' => $this->movement_type,
        ];
    }
}
