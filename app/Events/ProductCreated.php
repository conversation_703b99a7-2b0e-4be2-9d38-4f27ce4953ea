<?php

namespace App\Events;

use App\Models\Product;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProductCreated implements ShouldBroadcast
{
    use Dispatchable;
    use InteractsWithSockets;
    use SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(private Product $product)
    {
    }

    // Comentado temporalmente para probar
    // public function broadcastAs()
    // {
    //     return 'product-created';
    // }

    public function broadcastWith()
    {
        try {
            // Intentar cargar la categoría solo si el producto existe en la base de datos
            if ($this->product->exists) {
                $product = $this->product->load(['category']);
            } else {
                $product = $this->product;
            }

            return [
                'product' => $product,
                'message' => 'Producto creado exitosamente',
            ];
        } catch (\Exception $e) {
            // En caso de error, devolver solo los datos básicos del producto
            return [
                'product' => [
                    'id' => $this->product->id,
                    'name' => $this->product->name,
                    'barcode' => $this->product->barcode,
                    'description' => $this->product->description ?? null,
                ],
                'message' => 'Producto creado exitosamente',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('product-created-external'),
        ];
    }
}
