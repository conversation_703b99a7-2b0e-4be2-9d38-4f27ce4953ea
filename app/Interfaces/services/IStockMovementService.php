<?php

namespace App\Interfaces\services;

use App\Dtos\CreateProductDto;
use App\Models\Local;
use App\Models\LocalProduct;
use App\Models\Product;

interface IStockMovementService
{
    /**
     * Create initial stock for a new product
     *
     * @param Product $product The newly created product
     * @param int $initialStock The initial stock quantity
     * @param Local|null $local The local where the stock will be added (optional)
     * @param string|null $expirationDate The expiration date for the product (optional)
     * @return bool Whether the operation was successful
     */
    public function createInitialStock(Product $product, CreateProductDto $createProductDto, LocalProduct $localProduct): bool;
}
