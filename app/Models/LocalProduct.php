<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocalProduct extends Model
{
    /** @use HasFactory<\Database\Factories\LocalProductFactory> */
    use HasFactory;

    protected $fillable = ['local_id', 'product_id', 'stock', 'price', 'min_stock', 'max_stock', 'expiration_date', 'status'];

    public function local()
    {
        return $this->belongsTo(Local::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
