<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    /** @use HasFactory<\Database\Factories\ProductFactory> */
    use HasFactory;

    protected $fillable = ['name', 'barcode', 'description', 'image', 'category_id', 'expiration_date'];

    public function stock()
    {
        return $this->hasMany(LocalProduct::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function locals()
    {
        return $this->belongsToMany(Local::class, 'local_products');
    }

    public function batches()
    {
        return $this->hasMany(Batch::class);
    }
}
