<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StockMovement extends Model
{
    /** @use HasFactory<\Database\Factories\StockMovementFactory> */
    use HasFactory;

    protected $fillable = ['local_products_id', 'quantity', 'stock_movement_types_id', 'order_id', 'movement_reason', 'batch_id'];

    public function type()
    {
        return $this->belongsTo(StockMovementType::class, 'stock_movement_types_id');
    }

    public function localProduct()
    {
        return $this->belongsTo(LocalProduct::class, 'local_products_id');
    }

    public function batch()
    {
        return $this->belongsTo(Batch::class);
    }
}
