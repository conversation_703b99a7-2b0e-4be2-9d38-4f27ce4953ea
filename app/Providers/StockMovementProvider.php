<?php

namespace App\Providers;

use App\Factories\StockMovementStrategyFactory;
use App\Interfaces\StockMovementStrategy\StockMovementStrategy;
use App\Strategies\StockMovement\StockEntry;
use App\Strategies\StockMovement\StockExit;
use Illuminate\Support\ServiceProvider;

class StockMovementProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->bind(StockEntry::class);
        $this->app->bind(StockExit::class);

        $this->app->bind(StockMovementStrategy::class.'[]', function ($app) {
            return [
                $app->make(StockEntry::class),
                $app->make(StockExit::class),
            ];
        });

        $this->app->bind(StockMovementStrategyFactory::class, function ($app) {
            return new StockMovementStrategyFactory($app->make(StockMovementStrategy::class.'[]'));
        });
    }
}
