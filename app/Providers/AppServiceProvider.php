<?php

namespace App\Providers;

use App\Commands\StockEntryCommand;
use App\Interfaces\services\ILocalProductService;
use App\Interfaces\services\IProductService;
use App\Interfaces\services\IStockMovementService;
use App\Repositories\ProductRepository;
use App\Repositories\ProductRepositoryInterface;
use App\Services\LocalProduct\LocalProductService;
use App\Services\Product\ProductService;
use App\Services\StockMovement\StockMovementService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(StockEntryCommand::class);
        $this->app->bind(IProductService::class, ProductService::class);
        $this->app->bind(IStockMovementService::class, StockMovementService::class);
        $this->app->bind(ProductRepositoryInterface::class, ProductRepository::class);
        $this->app->bind(ILocalProductService::class, LocalProductService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
    }
}
