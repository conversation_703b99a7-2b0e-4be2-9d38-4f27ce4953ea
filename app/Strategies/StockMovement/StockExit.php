<?php

namespace App\Strategies\StockMovement;

use App\Dtos\StockEntryDto;
use App\Interfaces\StockMovementStrategy\StockMovementStrategy;
use App\Models\LocalProduct;
use App\Models\StockMovement;
use App\Models\StockMovementType;

class StockExit implements StockMovementStrategy
{
    public function supports(StockEntryDto $stockEntryDto): bool
    {
        return 'Salida de Stock' === $stockEntryDto->movement_type;
    }

    public function execute($stockExitDto, LocalProduct $localProduct): StockMovement
    {
        return StockMovement::create([
            'local_products_id' => $localProduct->id,
            'quantity' => -$stockExitDto->quantity,
            'stock_movement_types_id' => StockMovementType::where('name', 'Salida de Stock')->first()->id,
            'movement_reason' => $stockExitDto->movement_reason,
            'batch_id' => $stockExitDto->batch_id,
            'order_id' => null,
        ]);

        $localProduct->decrement('stock', $stockEntryDto->quantity);
    }
}
