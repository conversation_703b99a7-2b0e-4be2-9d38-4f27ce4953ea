<?php

namespace App\Repositories;

interface ProductRepositoryInterface extends RepositoryInterface
{
    /**
     * Find a product by barcode.
     *
     * @return \App\Models\Product|null
     */
    public function findByBarcode(string $barcode);

    /**
     * Get products by category ID.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByCategory(int $categoryId);

    /**
     * Get products with stock in a specific local.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProductsWithStockByLocal(int $localId);
}
