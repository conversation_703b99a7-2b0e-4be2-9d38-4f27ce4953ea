<?php

namespace App\Repositories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;

class ProductRepository extends BaseRepository implements ProductRepositoryInterface
{
    /**
     * ProductRepository constructor.
     */
    public function __construct(Product $model)
    {
        parent::__construct($model);
    }

    /**
     * Find a product by barcode.
     *
     * @return Product|null
     */
    public function findByBarcode(string $barcode)
    {
        return $this->model->where('barcode', $barcode)->with(['category', 'batches', 'stock'])->first();
    }

    /**
     * Get products by category ID.
     *
     * @return Collection
     */
    public function getByCategory(int $categoryId)
    {
        return $this->model->where('category_id', $categoryId)->get();
    }

    /**
     * Get products with stock in a specific local.
     *
     * @return Collection
     */
    public function getProductsWithStockByLocal(int $localId)
    {
        return $this->model->whereHas('stock', function ($query) use ($localId) {
            $query->where('local_id', $localId)->where('stock', '>', 0);
        })->get();
    }
}
