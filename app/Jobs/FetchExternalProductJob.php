<?php

namespace App\Jobs;

use App\Events\ExternalDatabaseBusy;
use App\Events\ProductCreated;
use App\Http\Client\OpenFoodFactsClient;
use App\Models\User;
use App\Repositories\ProductRepositoryInterface;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class FetchExternalProductJob implements ShouldQueue
{
    use Queueable;

    public $tries = 5;
    public $backoff = 30;

    /**
     * Create a new job instance.
     */
    public function __construct(public string $barcode, public User $user)
    {
    }

    /**
     * Execute the job.
     */
    public function handle(OpenFoodFactsClient $client, ProductRepositoryInterface $productRepository): void
    {
        try {
            $productData = $client->searchProduct($this->barcode);
            if ($productData) {
                $product = $productRepository->create($productData);
                broadcast(new ProductCreated($product));
            }

            broadcast(new ExternalDatabaseBusy());
        } catch (\Throwable $th) {
            dd($th->getMessage());
            throw $th;
        }
    }
}
