<?php

namespace App\Commands;

use App\Dtos\StockEntryDto;
use App\Factories\StockMovementFactory;
use App\Factories\StockMovementStrategyFactory;
use App\Models\LocalProduct;
use App\Models\StockMovement;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockEntryCommand
{
    public function __construct(
        private StockMovementStrategyFactory $stockMovementStrategy
    ) {
    }

    /**
     * Execute the stock entry command
     */
    public function execute(StockEntryDto $stockEntryDto): array
    {

            try {
                // Find the local product
                $localProduct = $this->findLocalProduct($stockEntryDto);

                // Validate stock operation
                $this->validateStockOperation($stockEntryDto,$localProduct);

                $stockMovement = $this->stockMovementStrategy->execute($stockEntryDto, $localProduct);

                // Update the local product stock
                $this->updateLocalProductStock($stockEntryDto,$localProduct);

                // Log the successful operation
                $this->logSuccessfulOperation($stockEntryDto, $localProduct, $stockMovement);

                return [
                    'success' => true,
                    'message' => 'Stock ingresado correctamente',
                    'data' => [
                        'stock_movement' => $stockMovement->load(['type', 'localProduct.product', 'batch']),
                        'local_product' => $localProduct->fresh(),
                        'new_stock' => $localProduct->fresh()->stock,
                    ]
                ];

            } catch (\Exception $e) {
                Log::error('Stock entry failed', [
                    'error' => $e->getMessage(),
                    'stock_entry_data' => $stockEntryDto->toArray(),
                    'trace' => $e->getTraceAsString()
                ]);

                throw $e;
            }

    }

    /**
     * Find the local product
     */
    private function findLocalProduct(StockEntryDto $stockEntryDto): LocalProduct
    {
        $localProduct = LocalProduct::where('product_id', $stockEntryDto->product_id)
            ->where('local_id', $stockEntryDto->local_id)
            ->first();

        if (!$localProduct) {
            throw new \Exception(
                "El producto no existe en el local seleccionado. " .
                "Product ID: {$stockEntryDto->product_id}, Local ID: {$stockEntryDto->local_id}"
            );
        }

        return $localProduct;
    }

    /**
     * Validate the stock operation
     */
    private function validateStockOperation(StockEntryDto $stockEntryDto,LocalProduct $localProduct): void
    {
        // Check if the product is active
        if (!$localProduct->status) {
            throw new \Exception('El producto está inactivo en este local');
        }

        // Validate quantity limits
        if ($stockEntryDto->quantity <= 0) {
            throw new \Exception('La cantidad debe ser mayor a 0');
        }

        // Check if the new stock would exceed maximum stock
        $newStock = $localProduct->stock + $stockEntryDto->quantity;
        if ($newStock > $localProduct->max_stock) {
            throw new \Exception(
                "La cantidad ingresada excede el stock máximo permitido. " .
                "Stock actual: {$localProduct->stock}, Máximo: {$localProduct->max_stock}, " .
                "Intentando agregar: {$stockEntryDto->quantity}"
            );
        }
    }

    /**
     * Update the local product stock
     */
    private function updateLocalProductStock(StockEntryDto $stockEntryDto, LocalProduct $localProduct): void
    {
        $localProduct->increment('stock', $stockEntryDto->quantity);
    }

    /**
     * Log the successful operation
     */
    private function logSuccessfulOperation(StockEntryDto $stockEntryDto, LocalProduct $localProduct, StockMovement $stockMovement): void
    {
        Log::info('Stock entry completed successfully', [
            'stock_movement_id' => $stockMovement->id,
            'product_id' => $stockEntryDto->product_id,
            'local_id' => $stockEntryDto->local_id,
            'quantity_added' => $stockEntryDto->quantity,
            'previous_stock' => $localProduct->stock - $stockEntryDto->quantity,
            'new_stock' => $localProduct->stock,
            'user_id' => $stockEntryDto->user_id,
            'batch_id' => $stockEntryDto->batch_id,
        ]);
    }
}
