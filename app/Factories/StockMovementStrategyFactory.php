<?php

namespace App\Factories;

use App\Dtos\StockEntryDto;
use App\Models\LocalProduct;
use App\Models\StockMovement;

class StockMovementStrategyFactory
{
    /**
     * @param iterable<StockMovementStrategy> $strategies
     */
    public function __construct(private iterable $strategies){}

    /**
     * Create a stock entry movement.
     */
    public function execute(StockEntryDto $stockEntryDto, LocalProduct $localProduct): StockMovement
    {
        foreach ($this->strategies as $strategy) {
            if ($strategy->supports($stockEntryDto)) {
                return $strategy->execute($stockEntryDto, $localProduct);
            }
        }

        throw new \Exception('No strategy found for stock movement');
    }
}
