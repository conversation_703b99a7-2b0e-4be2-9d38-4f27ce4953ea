<?php


namespace App\Services\LocalProduct;

use App\Dtos\CreateLocalProductDto;
use App\Interfaces\services\ILocalProductService;
use App\Models\LocalProduct;
use Illuminate\Support\Facades\DB;

class LocalProductService implements ILocalProductService
{
    public function createLocalProduct(CreateLocalProductDto $localProductDto)
    {
        DB::beginTransaction();
        try {
            $localProduct = LocalProduct::create([
                'local_id' => $localProductDto->local_id,
                'product_id' => $localProductDto->product_id,
                'stock' => $localProductDto->stock,
                'price' => $localProductDto->price,
                'min_stock' => $localProductDto->min_stock,
                'max_stock' => $localProductDto->max_stock,
                'expiration_date' => $localProductDto->expiration_date,
                'status' => $localProductDto->status,
            ]);

            DB::commit();

            return $localProduct;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
