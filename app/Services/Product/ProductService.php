<?php

namespace App\Services\Product;

use App\Dtos\CreateLocalProductDto;
use App\Dtos\CreateProductDto;
use App\Interfaces\services\ILocalProductService;
use App\Interfaces\services\IProductService;
use App\Interfaces\services\IStockMovementService;
use App\Jobs\FetchExternalProductJob;
use App\Models\Batch;
use App\Models\Local;
use App\Repositories\ProductRepositoryInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductService implements IProductService
{
    private ProductRepositoryInterface $productRepository;
    private IStockMovementService $stockMovementService;
    private ILocalProductService $localProductService;

    /**
     * ProductService constructor.
     */
    public function __construct(
        ProductRepositoryInterface $productRepository,
        IStockMovementService $stockMovementService,
        ILocalProductService $localProductService
    ) {
        $this->productRepository = $productRepository;
        $this->stockMovementService = $stockMovementService;
        $this->localProductService = $localProductService;
    }


    public function getFilteredProducts(Local $local, ?string $productName, ?string $categoryId)
    {
        // Get products with their local_product information (including stock)
        $products = $local->products();

        if ($productName) {
            $products = $products->where('name', 'like', '%'.$productName.'%');
        }

        $products = $products->with(['stock', 'category']);

        if ($categoryId) {
            $products = $products->whereHas('category', function ($query) use ($categoryId) {
                $query->where('id', '=', $categoryId);
            });
        }

        return $products;
    }

    public function findByBarcode(string $barcode)
    {
        $product = $this->productRepository->findByBarcode($barcode);

        if (!$product) {
            FetchExternalProductJob::dispatch($barcode, Auth::user());
            return null;
        }

        return $product;
    }

    public function createProduct(CreateProductDto $productRequestData)
    {
        $user = Auth::user();
        $localUser = DB::table('local_users')
                    ->where('user_id', $user->id ?? 1)
                    ->first();

        try {
            DB::beginTransaction();

            // Check if product with barcode already exists
            $existingProduct = $this->productRepository->findByBarcode($productRequestData->barcode);
            if ($existingProduct) {
                throw new \Exception('El codigo de barra ya fue registrado, verifica si ya fue registrado este producto.');
            }

            // Create product using repository
            $productData = [
                'name' => $productRequestData->name,
                'description' => $productRequestData->description,
                'barcode' => $productRequestData->barcode,
                'category_id' => $productRequestData->category_id,
                'image' => $productRequestData->image ?? 'https://img.freepik.com/premium-vector/new-product-banner-template-design_579179-1372.jpg?semt=ais_hybrid&w=740',
                'expiration_date' => $productRequestData->expiration_date,
            ];

            $product = $this->productRepository->create($productData);

            // Handle batch association if provided
            if ($productRequestData->batch_id) {
                $batch = Batch::find($productRequestData->batch_id);
                if ($batch) {
                    $batch->product_id = $product->id;
                    $batch->save();
                }
            }

            $localProductDto = new CreateLocalProductDto(
                $localUser->local_id,
                $product->id,
                $productRequestData->initial_stock,
                $productRequestData->price, // Default price, can be updated later
                $productRequestData->initial_stock, // Default min stock
                100, // Default max stock
                $productRequestData->expiration_date ?? now()->addYear(),
                true // Set status to active
            );

            // Create local product record
            $localProduct = $this->localProductService->createLocalProduct($localProductDto);

            // Handle initial stock if provided
            if (isset($productRequestData->initial_stock) && $productRequestData->initial_stock > 0) {
                // Use the StockMovementService to create initial stock
                $success = $this->stockMovementService->createInitialStock(
                    $product,
                    $productRequestData,
                    $localProduct,
                );

                if (!$success) {
                    Log::warning('Failed to create initial stock for product: '.$product->id);
                }
            }

            DB::commit();

            return $product;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
