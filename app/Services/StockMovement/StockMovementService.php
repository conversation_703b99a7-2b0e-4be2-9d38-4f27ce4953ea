<?php

namespace App\Services\StockMovement;

use App\Dtos\CreateProductDto;
use App\Interfaces\services\ILocalProductService;
use App\Interfaces\services\IStockMovementService;
use App\Models\LocalProduct;
use App\Models\Product;
use App\Models\StockMovement;
use App\Models\StockMovementType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockMovementService implements IStockMovementService
{
    public function __construct(private ILocalProductService $localProductService)
    {
    }

    public function createInitialStock(Product $product, CreateProductDto $productRequestData, LocalProduct $localProduct): bool
    {
        // If no local is provided, get the local from the authenticated user

        // Find or create the "new product" stock movement type
        $stockMovementType = StockMovementType::firstOrCreate(
            ['name' => 'Nuevo Producto'],
            ['description' => 'Registro inicial de stock para un nuevo producto']
        );

        // Start a transaction for local product and stock movement
        DB::beginTransaction();
        try {
            // Create stock movement record after local product is created
            StockMovement::create([
                'local_products_id' => $localProduct->id,
                'quantity' => $productRequestData->initial_stock,
                'stock_movement_types_id' => $stockMovementType->id,
                'movement_reason' => 'Registro inicial de producto',
            ]);

            // Commit the transaction
            DB::commit();

            Log::info('Created product with initial stock: Product ID='.$product->id.
                     ', Local ID='.$localProduct->local_id.
                     ', Stock='.$productRequestData->initial_stock);

            return true;
        } catch (\Exception $e) {
            // Rollback the transaction if there's an error
            DB::rollBack();
            // Log the error
            Log::error('Error creating local product or stock movement: '.$e->getMessage());
            throw new \Exception('Error creating local product or stock movement: '.$e->getMessage());

            return false;
        }
    }
}
