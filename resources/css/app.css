@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    margin: 0;
    min-width: 320px;
    min-height: 100vh;
  }

  @layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;
        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;
        --primary: 221.2 83.2% 53.3%;
        --primary-foreground: 210 40% 98%;
        --secondary: 210 40% 88.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;
        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;
        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;
        --border:214.3 31.8% 91.4%;
        --input:214.3 31.8% 91.4%;
        --ring:221.2 83.2% 53.3%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 0 0% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 0 0% 94%;
        --sidebar-accent-foreground: 0 0% 30%;
        --sidebar-border: 0 0% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }

    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 6.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 84% 60%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 0 0% 7%;
        --sidebar-foreground: 0 0% 95.9%;
        --sidebar-primary: 360, 100%, 100%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 0 0% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 0 0% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

  @layer components {
    .container-custom {
      @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    .section-spacing {
      @apply py-16 md:py-24;
    }

    .title-large {
      @apply text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight;
    }

    .title-medium {
      @apply text-2xl md:text-3xl font-bold tracking-tight;
    }

    .title-small {
      @apply text-xl md:text-2xl font-semibold tracking-tight;
    }

    .btn-primary {
      @apply inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-primary-foreground bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-200;
    }

    .btn-secondary {
      @apply inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-sm text-secondary-foreground bg-secondary hover:bg-primary hover:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary transition-colors duration-200;
    }

    .hover-lift {
      @apply transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg;
    }

    .card {
      @apply bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
    }

    .glass-card {
      @apply bg-white/70 backdrop-blur-lg rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300;
    }
  }
