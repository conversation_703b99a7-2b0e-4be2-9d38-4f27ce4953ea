
import { BrowserMultiFormatReader, NotFoundException } from "@zxing/library";
import { onBeforeMount, ref, ShallowRef } from "vue";

export function useBarcodeReader(videoElement: ShallowRef<HTMLVideoElement | null>) {

    // Create a new instance of BrowserMultiFormatReader
    const inputDevices = ref<MediaDeviceInfo[]>([]);
    const loadingReader = ref(true);
    const loadingDevices = ref(true);
    const codeReader = new BrowserMultiFormatReader();
    const decodeValue = ref<string | null>(null);
    const error = ref<string | null>(null);
    const isScanning = ref(false);
    const selectedDeviceId = ref(null);
    const stream = ref<MediaProvider | null >(null);

    onBeforeMount(async () => {
        await loadDevices();
        await startScanning();
    })

    const loadDevices = async () => {
        loadingDevices.value = true;
        try {
            inputDevices.value = await codeReader.listVideoInputDevices();
        } catch (err: any) {
            console.log('error precargando camaras', err.message)
        } finally {
            loadingDevices.value = false;
        }

    }


    const initReader = async () => {
        loadingReader.value = true;
        try {
            error.value = null;

            if (!videoElement.value) {
                await new Promise(resolve => {
                    const checkVideo = setInterval(() => {
                        if (videoElement.value) {
                            clearInterval(checkVideo);
                            resolve(true);
                        }
                    }, 10);
                })
            }

            if (inputDevices.value.length === 0) {
                throw new Error('no video input devices found');
            }

            return true;

        } catch (err: any) {
            error.value = err.message;
            console.error('Error initializing barcode reader:', err);
            return false;
        } finally {
            loadingReader.value = false;
        }
    }

    const startScanning = async (deviceId = selectedDeviceId.value) => {
        try {
            if (!deviceId) {
                const initialized = await initReader();
                if (!initialized) return;
            }


            isScanning.value = true;
            await codeReader.decodeFromVideoDevice(deviceId, videoElement.value, (result, err) => {
                isScanning.value = false;
                stream.value = videoElement.value?.srcObject ?? null;
                if (result) {
                    decodeValue.value = result.getText();
                }

                if (err && !(err instanceof NotFoundException)) {
                    error.value = err.message;
                }
            })

        } catch (err: any) {
            error.value = err.message
        } finally {

        }
    }


    const stopScanning = () => {
        codeReader.reset();

    }

    const switchCamera = async (deviceId: any) => {
        await startScanning(deviceId);
    }


    return {
        inputDevices,
        loadingReader,
        isScanning,
        initReader,
        startScanning,
        selectedDeviceId,
        switchCamera,
        stopScanning,
        decodeValue,
        error,
        stream,
        loadDevices,
        loadingDevices
    }
}
