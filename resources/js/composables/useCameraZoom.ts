import { ref, onUnmounted, ShallowRef } from 'vue';

// Define custom interfaces for MediaTrackCapabilities and MediaTrackSettings
interface ExtendedMediaTrackCapabilities extends MediaTrackCapabilities {
    zoom?: {
        min?: number;
        max?: number;
        step?: number;
    };
}

interface ExtendedMediaTrackSettings extends MediaTrackSettings {
    zoom?: number;
}

// Define custom interface for MediaTrackConstraintSet
interface ExtendedMediaTrackConstraintSet extends MediaTrackConstraintSet {
    zoom?: number;
    focusDistance?: number;
}

export function useCameraZoom(videoElement: ShallowRef<HTMLVideoElement | null>, zoomControlElement: ShallowRef<HTMLInputElement | null>) {
    const zoom = ref(1);
    const minZoom = ref(1);
    const maxZoom = ref(10);
    const zoomStep = ref(0.1);
    const isZoomSupported = ref(true);
    let videoTrack: MediaStreamTrack | null = null;

    const initZoom = async () => {
        try {

            // Obtener el stream del elemento video
            const stream = videoElement.value?.srcObject ?? null;
            if (!stream) return;

            videoTrack = (stream as MediaStream).getVideoTracks()[0];
            console.log(videoTrack);
            if (!videoTrack || typeof videoTrack.getCapabilities !== 'function') {
                console.log('Zoom no soportado en este dispositivo');
                return;
            }

            const capabilities = videoTrack.getCapabilities() as ExtendedMediaTrackCapabilities;
            const settings = videoTrack.getSettings() as ExtendedMediaTrackSettings;

            if ('zoom' in capabilities) {
                isZoomSupported.value = true;
                minZoom.value = capabilities.zoom?.min || 1;
                maxZoom.value = capabilities.zoom?.max || 10;
                zoomStep.value = capabilities.zoom?.step || 0.1;
                zoom.value = settings.zoom || 1;

                // Configurar el control de zoom
                if (zoomControlElement.value) {
                    zoomControlElement.value.min = String(minZoom.value);
                    zoomControlElement.value.max = String(maxZoom.value);
                    zoomControlElement.value.step = String(zoomStep.value);
                    zoomControlElement.value.value = String(zoom.value);
                    // Configurar evento
                    zoomControlElement.value.addEventListener('input', handleZoomChange);
                }
            }
        } catch (error) {
            console.error('Error initializing camera zoom:', error);
        }
    };

    const handleZoomChange = (event: Event) => {
        if (!isZoomSupported.value || !videoTrack) return;

        const inputElement = event.target as HTMLInputElement;
        const newZoom = parseFloat(inputElement.value);
        zoom.value = newZoom;

        try {
            videoTrack.applyConstraints({
                advanced: [{ zoom: newZoom } as ExtendedMediaTrackConstraintSet]
            });
        } catch (error) {
            console.error('Error applying zoom:', error);
        }
    };

    const setZoom = (value: number) => {
        if (!isZoomSupported.value || !videoTrack) return;

        const clampedValue = Math.max(minZoom.value, Math.min(maxZoom.value, value));
        zoom.value = clampedValue;

        if (zoomControlElement.value) {
            zoomControlElement.value.value = String(clampedValue);
        }

        try {
            videoTrack.applyConstraints({
                advanced: [{
                    zoom: clampedValue,
                    focusDistance: clampedValue
                } as ExtendedMediaTrackConstraintSet]
            });
        } catch (error) {
            console.error('Error setting zoom:', error);
        }
    };

    const cleanup = () => {
        if (zoomControlElement.value) {
            zoomControlElement.value.removeEventListener('input', handleZoomChange);
        }
    };

    onUnmounted(cleanup);

    // // Observar cambios en el elemento video
    // watch([videoElement, zoomControlElement], (newVal) => {
    //     if (newVal) initZoom();
    // }, { immediate: true });

    return {
        zoom,
        minZoom,
        maxZoom,
        zoomStep,
        isZoomSupported,
        setZoom,
        initZoom
    };
}
