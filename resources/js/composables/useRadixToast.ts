// composables/useRadixToast.js
import { ref } from 'vue'

let idCounter = 0

const toasts = ref<Toast[]>([])

interface Toast {
    id: number;
    title: string;
    description: string;
    variant: string;
    duration: number;
}

export function useRadixToast() {
  const showToast = ({id = 1, title, description, variant = 'default', duration = 3000 }: Toast) => {
    const newId = ++idCounter
    const toast = { id:newId, title, description, variant, duration }

    toasts.value.push(toast)

    setTimeout(() => {
      toasts.value = toasts.value.filter((t) => t.id !== id)
    }, duration)
  }

  const showSuccess = (message: string) => {
    showToast({
      id: idCounter,
      title: 'Éxito',
      description: message,
      variant: 'success',
      duration: 3000,
    })
  }

  const showError = (message: string) => {
    showToast({
      id: idCounter,
      title: 'Error',
      description: message,
      variant: 'error',
      duration: 3000
    })
  }

  return { toasts, showSuccess, showError }
}
