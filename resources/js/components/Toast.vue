<script setup lang="ts">
import { ToastAction, ToastClose, ToastDescription, ToastRoot, ToastTitle } from 'radix-vue';

defineProps<{
    title: string
    content: string
}>()

</script>

<template>
    <ToastRoot>
        <ToastTitle>
            {{ title }}
        </ToastTitle>
        <ToastDescription>
            {{ content }}
        </ToastDescription>
        <ToastAction
            as-child
            alt-text="toast"
        >
            <slot />
        </ToastAction>
        <ToastClose>
            <span aria-hidden>X</span>
        </ToastClose>
    </ToastRoot>

</template>
