<script setup lang="ts">
import { FileUpIcon, XIcon } from 'lucide-vue-next';
import { ref } from 'vue';

const importOptions = ref({
    overwrite: false,
    skipHeader: true,
    validate: true
});

const emits = defineEmits(['close']);

</script>

<template>
    <div class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
        <div
            class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 rounded-lg">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Importar Productos desde CSV</h3>
                <button @click="emits('close')" class="rounded-full p-2 hover:bg-muted/50">
                    <XIcon class="h-4 w-4" />
                </button>
            </div>

            <div class="flex flex-col gap-4">
                <p class="text-sm text-muted-foreground">Sube un archivo CSV para importar productos a tu inventario.
                </p>

                <div class="flex items-center justify-center w-full">
                    <label for="dropzone-file"
                        class="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-background hover:bg-accent/10">
                        <div class="flex flex-col items-center justify-center pt-5 pb-6">
                            <FileUpIcon class="w-8 h-8 mb-2 text-muted-foreground" />
                            <p class="mb-2 text-sm text-foreground">
                                <span class="font-semibold">Haz click para subir archivo</span> o arrastra aquí tu
                                archivo
                            </p>
                            <p class="text-xs text-muted-foreground">CSV or Excel</p>
                        </div>
                        <input id="dropzone-file" type="file" class="hidden" />
                    </label>
                </div>

                <div class="rounded-md border bg-muted/30 p-4">
                    <h4 class="text-sm font-medium">Import Options</h4>
                    <div class="mt-3 space-y-3">
                        <div class="flex items-center gap-2">
                            <input type="checkbox" id="overwrite" v-model="importOptions.overwrite"
                                class="h-4 w-4 rounded border-input bg-background text-primary focus:outline-none focus:ring-1 focus:ring-ring" />
                            <label for="overwrite" class="text-sm">Overwrite existing products</label>
                        </div>
                        <div class="flex items-center gap-2">
                            <input type="checkbox" id="skipheader" v-model="importOptions.skipHeader"
                                class="h-4 w-4 rounded border-input bg-background text-primary focus:outline-none focus:ring-1 focus:ring-ring" />
                            <label for="skipheader" class="text-sm">First row contains headers</label>
                        </div>
                        <div class="flex items-center gap-2">
                            <input type="checkbox" id="validate" v-model="importOptions.validate"
                                class="h-4 w-4 rounded border-input bg-background text-primary focus:outline-none focus:ring-1 focus:ring-ring" />
                            <label for="validate" class="text-sm">Validate before import</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-between items-center">
                <a href="#" class="text-sm text-muted-foreground hover:text-primary underline">
                    Download template
                </a>
                <div class="flex space-x-2">
                    <button @click="emits('close')"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2">
                        Cancel
                    </button>
                    <button
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-4 py-2">
                        Start Import
                    </button>
                </div>
            </div>
        </div>
    </div>

</template>
