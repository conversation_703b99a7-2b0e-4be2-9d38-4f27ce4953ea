<script setup lang="ts">
import { XIcon, AlertTriangleIcon } from 'lucide-vue-next';
import { ref } from 'vue';
import axios from 'axios';
import { useRadixToast } from '@/composables/useRadixToast';

interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category_id: number;
    expiration_date: string | null;
    category: {
        id: number;
        name: string;
    };
}

interface Local {
    id: number;
    name: string;
}

const props = defineProps<{
    product: Product;
    local: Local;
}>();

const isLoading = ref(false);
const { showSuccess, showError } = useRadixToast();

const emits = defineEmits(['close', 'product-added-to-local']);

const handleConfirm = async () => {
    isLoading.value = true;

    try {
        const response = await axios.post(route('add-product-to-local'), {
            product_id: props.product.id,
            local_id: props.local.id,
        });

        const data = response.data;

        if (data.success) {
            showSuccess(data.message || `Producto agregado al local ${props.local.name} correctamente`);
            // Emit event with the updated product data
            if (data.product) {
                emits('product-added-to-local', data.product);
            } else {
                emits('close');
            }
        } else {
            throw new Error(data.message || 'Error al agregar el producto al local');
        }
    } catch (error: any) {
        console.error('Error:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Error al agregar el producto al local';
        showError(errorMessage);
    } finally {
        isLoading.value = false;
    }
};

const handleCancel = () => {
    emits('close');
};
</script>

<template>
    <div class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
        <div
            class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-md translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 rounded-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <AlertTriangleIcon class="h-5 w-5 text-amber-500" />
                    <h3 class="text-lg font-semibold">Producto no encontrado en local</h3>
                </div>
                <button @click="handleCancel" class="rounded-full p-2 hover:bg-muted/50">
                    <XIcon class="h-4 w-4" />
                </button>
            </div>

            <div class="space-y-4">
                <div class="text-sm text-muted-foreground">
                    <p>El producto <strong>{{ product.name }}</strong> no existe en el local <strong>{{ local.name
                            }}</strong>.</p>
                    <p class="mt-2">¿Deseas agregarlo a este local con stock inicial de 0?</p>
                </div>

                <!-- Product Info -->
                <div class="p-3 border rounded-lg bg-muted/30">
                    <h4 class="font-medium text-sm mb-2">Información del Producto</h4>
                    <div class="space-y-1 text-xs">
                        <div class="flex justify-between">
                            <span class="text-muted-foreground">Nombre:</span>
                            <span class="font-medium">{{ product.name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-muted-foreground">Código:</span>
                            <span class="font-mono">{{ product.barcode }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-muted-foreground">Categoría:</span>
                            <span>{{ product.category?.name }}</span>
                        </div>
                    </div>
                </div>

                <div
                    class="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                    <p><strong>Nota:</strong> El producto se agregará con:</p>
                    <ul class="mt-1 ml-4 list-disc space-y-1">
                        <li>Stock inicial: 0</li>
                        <li>Precio: $0 (podrás actualizarlo después)</li>
                        <li>Estado: Activo</li>
                    </ul>
                </div>
            </div>

            <div class="flex justify-end space-x-2">
                <button type="button" @click="handleCancel"
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                    Cancelar
                </button>
                <button type="button" @click="handleConfirm" :disabled="isLoading"
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                    {{ isLoading ? 'Agregando...' : 'Sí, agregar al local' }}
                </button>
            </div>
        </div>
    </div>
</template>
