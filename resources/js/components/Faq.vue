<script setup lang="ts">
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { ChevronDownIcon } from '@heroicons/vue/24/outline'

const faqs = [
    {
        question: "¿Necesito conocimientos técnicos para usar LocalWMS?",
        answer: "No, LocalWMS está diseñado para ser intuitivo y fácil de usar. No se requieren conocimientos técnicos especiales. Ofrecemos capacitación inicial y tutoriales dentro de la aplicación para ayudarte a comenzar rápidamente."
    },
    {
        question: "¿Qué hardware necesito para implementar el sistema?",
        answer: "Lo mínimo que necesitas es un smartphone o tablet con conexión a internet. Para una experiencia óptima, recomendamos también tener un computador y, opcionalmente, una impresora de tickets y un lector de códigos de barras para agilizar los procesos."
    },
    {
        question: "¿Puedo importar mi inventario actual?",
        answer: "Sí, facilitamos la migración de datos desde otros sistemas o desde archivos Excel. Nuestro equipo de soporte te guiará en el proceso de importación para que no pierdas ningún dato importante."
    },
    {
        question: "¿El sistema funciona sin conexión a internet?",
        answer: "La aplicación móvil puede funcionar temporalmente sin conexión, almacenando las operaciones realizadas y sincronizándolas cuando se restablezca la conexión. Sin embargo, para funcionalidades completas y datos en tiempo real, se recomienda tener conexión a internet."
    },
    {
        question: "¿Cómo puedo obtener soporte si tengo problemas?",
        answer: "Ofrecemos soporte por email, chat y teléfono dependiendo de tu plan. También contamos con una base de conocimientos completa con tutoriales y preguntas frecuentes. Todos los planes incluyen al menos soporte por email con tiempos de respuesta garantizados."
    },
    {
        question: "¿Puedo cancelar mi suscripción en cualquier momento?",
        answer: "Sí, puedes cancelar tu suscripción cuando lo desees sin penalizaciones. Ofrecemos planes mensuales para mayor flexibilidad, aunque los planes anuales tienen un descuento significativo."
    }
]
</script>

<template>
    <section id="faq" class="section-spacing bg-gray-50">
        <div class="container-custom">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="title-medium text-gray-900 mb-4">Preguntas frecuentes</h2>
                <p class="text-lg text-gray-600">
                    Resolvemos tus dudas sobre LocalWMS. Si tienes más preguntas, no dudes en contactarnos.
                </p>
            </div>

            <div class="max-w-3xl mx-auto divide-y divide-gray-200">
                <Disclosure v-for="(faq, index) in faqs" :key="index" v-slot="{ open }" as="div" class="py-6">
                    <DisclosureButton class="flex w-full justify-between items-center text-left">
                        <h3 class="text-lg font-medium text-gray-900">{{ faq.question }}</h3>
                        <ChevronDownIcon class="h-5 w-5 text-primary transition-transform duration-300"
                            :class="{ 'transform rotate-180': open }" />
                    </DisclosureButton>
                    <DisclosurePanel class="mt-4 text-gray-600">
                        {{ faq.answer }}
                    </DisclosurePanel>
                </Disclosure>
            </div>

            <div class="text-center mt-12">
                <p class="text-gray-600 mb-6">¿No encuentras la respuesta que buscas?</p>
                <a href="#contact" class="btn-secondary">
                    Contacta con nosotros
                </a>
            </div>
        </div>
    </section>
</template>
