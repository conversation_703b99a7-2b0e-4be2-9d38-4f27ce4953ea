<script setup lang="ts">
import { Link, router, usePage } from '@inertiajs/vue3';
import { ChevronLeftIcon, ChevronRightIcon, ClipboardIcon, EditIcon, PackageIcon, SearchIcon, Trash2Icon } from 'lucide-vue-next';
import { ref, watch } from 'vue';

// Define types for our data structure
interface LocalProduct {
    id: number;
    local_id: number;
    product_id: number;
    stock: number;
    price: number;
    min_stock: number;
    max_stock: number;
    expiration_date: string;
    status: boolean;
}

interface Category {
    id: number;
    name: string;
}

interface Product {
    id: number;
    name: string;
    barcode: string;
    description: string;
    image: string;
    category_id: number;
    category?: Category;
    stock: LocalProduct[];
}

const getStockStatusClass = (stock: number | undefined) => {
    if (!stock || stock === 0) return 'bg-destructive/10 text-destructive';
    if (stock <= 5) return 'bg-warning/10 text-warning';
    return 'bg-success/10 text-success';
};

const searchQuery = ref('');
let searchTimeout: ReturnType<typeof setTimeout> | null = null;

const handleSearch = () => {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    searchTimeout = setTimeout(() => {
        // Get categoryId from URL if it exists
        const urlParams = new URLSearchParams(window.location.search);
        const categoryId = urlParams.get('categoryId');

        router.visit('/inventory', {
            data: {
                productName: searchQuery.value || undefined,
                categoryId: categoryId || undefined
            },
            preserveScroll: true
        });
    }, 500);
    // 500ms debounce delay
};

const handleCategoryChange = (event: Event) => {
    const target = event.target as HTMLSelectElement;
    const categoryId = target.value;

    router.visit('/inventory', {
        data: {
            categoryId,
            productName: searchQuery.value || undefined
        },
        preserveScroll: true
    });
};

const page = usePage<{
    products: {
        next_page_url?: string;
        prev_page_url?: string;
        data: Product[];
        last_page: number;
        path: string;
        links: any;
        meta: any;
        total: number;
        from: number;
        to: number;
    },
    categories: Category[];
    url: string;
}>();

const categories = page.props.categories;

// Initialize searchQuery from URL if it exists
watch(() => window.location.search, (newSearch) => {
    const urlParams = new URLSearchParams(newSearch);
    const productNameParam = urlParams.get('productName');
    searchQuery.value = productNameParam || '';
}, { immediate: true });

// Using real data from the backend

</script>

<template>
    <div class="rounded-lg border bg-card overflow-hidden shadow-sm">
        <div class="p-6 flex flex-col sm:flex-row sm:items-center justify-between">
            <h3 class="text-lg font-semibold">Listado de productos</h3>

            <div class="mt-4 sm:mt-0 flex flex-col sm:flex-row gap-4">
                <div class="relative">
                    <SearchIcon class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <input type="search" placeholder="Buscar productos..." v-model="searchQuery" @input="handleSearch"
                        class="rounded-md border border-input bg-background pl-8 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 w-full" />
                </div>

                <select @change="handleCategoryChange"
                    class="h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                    <option value="">Categoria</option>
                    <option v-for="category in categories" :value="category.id" :key="category.id">{{ category.name }}
                    </option>
                </select>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full border-collapse">
                <thead>
                    <tr class="border-b text-sm">
                        <th class="py-3 px-4 text-left font-medium text-muted-foreground">Nombre</th>
                        <th class="py-3 px-4 text-left font-medium text-muted-foreground">Codigo de barras</th>
                        <th class="py-3 px-4 text-left font-medium text-muted-foreground">Categoria</th>
                        <th class="py-3 px-4 text-left font-medium text-muted-foreground">Precio</th>
                        <th class="py-3 px-4 text-left font-medium text-muted-foreground">Stock</th>
                        <th class="py-3 px-4 text-left font-medium text-muted-foreground">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="product in page.props.products.data" :key="product.id"
                        class="border-b hover:bg-muted/50">
                        <td class="py-3 px-4 text-sm">
                            <div class="flex items-center gap-3">
                                <div class="h-10 w-10 rounded-md bg-muted flex items-center justify-center">
                                    <PackageIcon class="h-5 w-5 text-muted-foreground" />
                                </div>
                                <div>
                                    <p class="font-medium">{{ product.name }}</p>
                                    <p class="text-xs text-muted-foreground">ID: {{ product.id }}</p>
                                </div>
                            </div>
                        </td>
                        <td class="py-3 px-4 font-mono text-xs">{{ product.barcode }}</td>
                        <td class="py-3 px-4 text-sm">{{ product.category?.name }}</td>
                        <td class="py-3 px-4 text-sm font-medium">{{ product.stock[0]?.price }}</td>
                        <td class="py-3 px-4 text-sm">
                            <span class="inline-flex rounded-full px-2 py-1 text-xs font-medium"
                                :class="getStockStatusClass(product.stock[0]?.stock)">
                                {{ product.stock[0]?.stock }}
                            </span>
                        </td>
                        <td class="py-3 px-4 text-sm">
                            <div class="flex space-x-2">
                                <button
                                    class="rounded-md p-1 text-muted-foreground hover:bg-muted hover:text-foreground">
                                    <EditIcon class="h-4 w-4" />
                                </button>
                                <button
                                    class="rounded-md p-1 text-muted-foreground hover:bg-muted hover:text-foreground">
                                    <ClipboardIcon class="h-4 w-4" />
                                </button>
                                <button
                                    class="rounded-md p-1 text-muted-foreground hover:bg-destructive/10 hover:text-destructive">
                                    <Trash2Icon class="h-4 w-4" />
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-between border-t p-4">
            <div class="text-sm text-muted-foreground">
                mostrando desde <strong>{{ page.props.products.from }}</strong> hasta <strong>{{ page.props.products.to
                    }}</strong> de un total <strong>{{
                        page.props.products.total }}</strong>
                productos
            </div>
            <div class="flex items-center space-x-2">
                <Link v-if="page.props.products.prev_page_url" :href="page.props.products.prev_page_url"
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0">
                <ChevronLeftIcon class="h-4 w-4" />
                </Link>
                <Link v-for="n in page.props.products.last_page" :href="page.props.products.path + '?page=' + n"
                    :key="n"
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-primary text-primary-foreground hover:bg-primary/90 h-8 w-8 p-0">
                {{ n }}
                </Link>
                <Link v-if="page.props.products.next_page_url" :href="page.props.products.next_page_url"
                    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-8 w-8 p-0">
                <ChevronRightIcon class="h-4 w-4" />
                </Link>
            </div>
        </div>
    </div>
</template>
