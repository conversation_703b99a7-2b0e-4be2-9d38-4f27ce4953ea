<!-- components/ToastManager.vue -->
<script setup lang="ts">
import {
  ToastRoot,
  ToastTitle,
  ToastDescription,
  ToastClose,
} from 'radix-vue'
import { X } from 'lucide-vue-next'
import { useRadixToast } from '@/composables/useRadixToast'

const { toasts } = useRadixToast()
</script>

<template>
  <div>
    <ToastRoot
      v-for="toast in toasts"
      :key="toast.id"
      class="rounded-md border p-4 shadow-lg bg-white mb-2"
    >
      <div class="flex justify-between items-center">
        <ToastTitle class="font-bold">{{ toast.title }}</ToastTitle>
        <ToastClose>
          <X class="w-4 h-4" />
        </ToastClose>
      </div>
      <ToastDescription class="text-sm text-gray-600">
        {{ toast.description }}
      </ToastDescription>
    </ToastRoot>
  </div>
</template>
