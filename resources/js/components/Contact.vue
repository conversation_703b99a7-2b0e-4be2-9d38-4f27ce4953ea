<script setup lang="ts">
import { ref } from 'vue'

const form = ref({
    name: '',
    email: '',
    phone: '',
    business: '',
    message: ''
})

const formSubmitted = ref(false)
const isSubmitting = ref(false)

const handleSubmit = async () => {
    isSubmitting.value = true

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    formSubmitted.value = true
    isSubmitting.value = false
}
</script>

<template>
    <section id="contact" class="section-spacing bg-white relative overflow-hidden">
        <!-- Decorative elements -->
        <div class="absolute top-0 left-0 transform -translate-x-1/4 -translate-y-1/4">
            <div class="w-96 h-96 bg-primary-50 rounded-full opacity-70 blur-3xl"></div>
        </div>
        <div class="absolute bottom-0 right-0 transform translate-x-1/4 translate-y-1/4">
            <div class="w-96 h-96 bg-secondary-50 rounded-full opacity-70 blur-3xl"></div>
        </div>

        <div class="container-custom relative z-10">
            <div class="max-w-5xl mx-auto">
                <div class="bg-gradient-to-r from-primary to-primary/60 rounded-2xl shadow-xl overflow-hidden">
                    <div class="grid md:grid-cols-2">
                        <div class="p-8 md:p-12 text-white">
                            <h2 class="text-3xl font-bold mb-6">¿Listo para optimizar tu negocio?</h2>
                            <p class="text-gray-100 mb-8 text-lg">
                                Solicita una demostración gratuita o consulta con nuestros especialistas. Estamos aquí
                                para ayudarte a encontrar la mejor solución para tu comercio.
                            </p>
                            <div class="space-y-6">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium">Demo personalizada</h3>
                                        <p class=" mt-1">Vea cómo LocalWMS puede adaptarse a sus
                                            necesidades específicas.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium">Implementación rápida</h3>
                                        <p class="mt-1">En menos de 24 horas podrá tener su sistema
                                            funcionando.</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium">Soporte continuo</h3>
                                        <p class="mt-1">Nuestro equipo estará con usted en cada paso
                                            del camino.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-8 md:p-12">
                            <div v-if="formSubmitted"
                                class="h-full flex flex-col items-center justify-center text-center">
                                <div class="rounded-full bg-success-100 p-3 mb-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-success-600"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-2">Gracias por contactarnos</h3>
                                <p class="text-gray-600 mb-6">Nos pondremos en contacto contigo en las próximas 24 horas
                                    para coordinar tu demostración gratuita.</p>
                                <button @click="formSubmitted = false" class="text-primary font-medium">
                                    Volver al formulario
                                </button>
                            </div>

                            <form v-else @submit.prevent="handleSubmit" class="space-y-6">
                                <h3 class="text-xl font-bold text-gray-900 mb-6">Solicita tu demo gratuita</h3>

                                <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
                                    <div>
                                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nombre
                                            completo</label>
                                        <input id="name" v-model="form.name" type="text" required
                                            class="block w-full h-7 border rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm" />
                                    </div>
                                    <div>
                                        <label for="email"
                                            class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                        <input id="email" v-model="form.email" type="email" required
                                            class="block w-full border h-7 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm" />
                                    </div>
                                    <div>
                                        <label for="phone"
                                            class="block text-sm font-medium text-gray-700 mb-1">Teléfono</label>
                                        <input id="phone" v-model="form.phone" type="tel"
                                            class="block w-full h-7 border rounded-md border-gray-300 shadow-sm focus:border-primary/50 focus:ring-primary sm:text-sm" />
                                    </div>
                                    <div>
                                        <label for="business"
                                            class="block text-sm font-medium text-gray-700 mb-1">Nombre del
                                            comercio</label>
                                        <input id="business" v-model="form.business" type="text" required
                                            class="block w-full h-7 border rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm" />
                                    </div>
                                    <div class="sm:col-span-2">
                                        <label for="message"
                                            class="block text-sm font-medium text-gray-700 mb-1">Mensaje
                                            (opcional)</label>
                                        <textarea id="message" v-model="form.message" rows="4"
                                            class="block w-full border rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"></textarea>
                                    </div>
                                </div>

                                <div>
                                    <button type="submit" :disabled="isSubmitting"
                                        class="w-full flex justify-center items-center font-bold py-3 px-4 border border-transparent rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                                stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor"
                                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                            </path>
                                        </svg>
                                        {{ isSubmitting ? 'Enviando...' : 'Solicitar Demo' }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>
