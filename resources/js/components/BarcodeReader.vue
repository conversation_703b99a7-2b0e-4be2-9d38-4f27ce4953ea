<script setup lang="ts">
import { onUnmounted, useTemplateRef, watch } from 'vue';
import { useBarcodeReader } from '../composables/useBarcodeReader';
import { useCameraZoom } from '../composables/useCameraZoom';

const emit = defineEmits(['decode']);

// const sleep = (ms) => new Promise((r) => setTimeout(r, ms));
const zoomElementRef = useTemplateRef('zoomElementRef')
const videoRef = useTemplateRef('videoRef');

const {
    decodeValue,
    loadingReader,
    error,
    isScanning,
    stopScanning,
    loadingDevices,
} = useBarcodeReader(videoRef);

const {
    minZoom,
    maxZoom,
    zoomStep,
    isZoomSupported,
    initZoom
} = useCameraZoom(videoRef, zoomElementRef);




watch(decodeValue, (newValue) => {
    console.log('new value', newValue);
    if (newValue !== '') {
        emit('decode', newValue);
    }
})

watch(isScanning, () => {
    if (videoRef.value?.srcObject) {
        console.log(videoRef.value.srcObject)
        initZoom();
    }
})

onUnmounted(() => {
    stopScanning();
})


</script>

<template>
    <div v-if="loadingDevices">
        <p>Loading devices...</p>
    </div>

    <div v-if="loadingReader">
        <p>Loading...</p>
    </div>

    <div v-if="error">
        <p>Error: {{ error }}</p>
    </div>

    <div v-if="isScanning && !loadingReader">
        <p>Scanning...</p>
    </div>
    <div class="grid grid-cols-2 sm:grid-cols-7">

        <video ref="videoRef" class="object-fill col-span-2 sm:col-span-6 " id="video">
        </video>

        <input v-if="isZoomSupported" class=" sm:rotate-90" ref="zoomElementRef" type="range" :min="minZoom"
            :max="maxZoom" :step="zoomStep" name="zoom" id="zoom" />

        <!-- <div v-if="isFocusSupported">
            <label for="focus">focus</label>
            <input ref="focusRef" type="range" :min="minFocus" :max="maxFocus" :step="focusStep" name="focus"
                id="focus" />
        </div>-->

    </div>
</template>
