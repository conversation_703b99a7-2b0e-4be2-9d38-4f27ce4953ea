<script setup lang="ts">

const features = [
    {
        id: 1,
        title: 'Gestión de Inventario',
        description: 'Control total sobre tu stock, entradas y salidas. Recibe alertas automáticas cuando tus productos alcanzan niveles mínimos.',
        icon: 'M20 3H4a2 2 0 00-2 2v14a2 2 0 002 2h16a2 2 0 002-2V5a2 2 0 00-2-2zm0 16H4V5h16v14zM9 17a1 1 0 100-2H5a1 1 0 100 2h4zm6-4a1 1 0 100-2h-4a1 1 0 100 2h4zm4-4a1 1 0 100-2h-8a1 1 0 100 2h8z'
    },
    {
        id: 2,
        title: 'Punto de Venta Integrado',
        description: 'Vende tus productos y automáticamente actualiza tu inventario. Sin doble carga de datos ni errores humanos.',
        icon: 'M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zm14 5H6a1 1 0 00-1 1v10a1 1 0 001 1h12a1 1 0 001-1V10a1 1 0 00-1-1zM9 13a1 1 0 011-1h4a1 1 0 110 2h-4a1 1 0 01-1-1z'
    },
    {
        id: 3,
        title: 'Informes y Análisis',
        description: 'Reportes detallados sobre ventas, productos más vendidos y rotación de inventario para tomar mejores decisiones.',
        icon: 'M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
    },
    {
        id: 4,
        title: 'Gestión de Proveedores',
        description: 'Administra tus proveedores, realiza pedidos y mantén un registro de precios y condiciones comerciales.',
        icon: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
    },
    {
        id: 5,
        title: 'Aplicación Móvil',
        description: 'Gestiona tu negocio desde cualquier lugar con nuestra aplicación móvil. Controla tu inventario incluso cuando no estás en la tienda.',
        icon: 'M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z'
    },
    {
        id: 6,
        title: 'Sincronización Multipunto',
        description: 'Si tienes varios locales, sincroniza el inventario entre todas tus sucursales. Traslada mercadería sin perder el control.',
        icon: 'M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z'
    }
]
</script>

<template>
    <section id="features" class="section-spacing bg-white">
        <div class="container-custom">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="title-medium text-gray-900 mb-4">Todo lo que necesitas para gestionar tu inventario</h2>
                <p class="text-lg text-gray-600">
                    LocalWMS combina potencia y simplicidad para que puedas enfocarte en lo que realmente importa:
                    atender a tus clientes.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div v-for="feature in features" :key="feature.id" class="card hover-lift group">
                    <div
                        class="bg-gray-100 rounded-lg w-12 h-12 flex items-center justify-center mb-4 group-hover:bg-primary group-hover:text-white transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="feature.icon" />
                        </svg>
                    </div>
                    <h3
                        class="title-small text-gray-900 mb-2 group-hover:text-primary transition-colors duration-300">
                        {{ feature.title }}</h3>
                    <p class="text-gray-600">{{ feature.description }}</p>
                </div>
            </div>

            <div class="mt-16 text-center">
                <a href="#benefits" class="btn-secondary">
                    Ver cómo te beneficia
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20"
                        fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l4.293-4.293a1 1 0 011.414 0z"
                            clip-rule="evenodd" />
                    </svg>
                </a>
            </div>
        </div>
    </section>
</template>
