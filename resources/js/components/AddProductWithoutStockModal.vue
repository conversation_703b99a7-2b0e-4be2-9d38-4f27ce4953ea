<script setup lang="ts">
import { ScanLineIcon, XIcon } from 'lucide-vue-next';
import { onMounted, ref } from 'vue';
import { useForm, usePage } from '@inertiajs/vue3';
import BarcodeReader from './BarcodeReader.vue';
import { useRadixToast } from '@/composables/useRadixToast';

interface Category {
    id: number;
    name: string;
}

interface Local {
    id: number;
    name: string;
}

interface Product {
    id: number;
    name: string;
    description: string;
    barcode: string;
    category_id: number;
    expiration_date: string | null;
    category: {
        id: number;
        name: string;
    };
}

const showScanner = ref(false);
const categories = ref<Category[]>([]);
const scannedCode = ref('');
const scannedCodes = ref<string[]>([]);

const page = usePage();

const form = useForm({
    name: '',
    description: '',
    barcode: '',
    category_id: '',
    local: '',
    price: '',
    status: false,
    initial_stock: '0', // Siempre será 0 para productos sin stock
});

const { showSuccess, showError } = useRadixToast();

onMounted(async () => {
    try {
        const response = await fetch('/categories');
        const data = await response.json();
        categories.value = data.data;
    } catch (error) {
        console.error(error);
    }
});

const emits = defineEmits(['close', 'product-created']);

const handleSubmit = () => {
    form.post(route('create-product'), {
        preserveScroll: true,
        onSuccess: (response) => {
            showSuccess('Producto creado correctamente');
            // Emitir evento con el producto creado
            console.log(response);
            if (response?.props?.product) {
                emits('product-created', response.props.product);
            } else {
                emits('close');
            }
        },
        onError: (errors) => {
            console.error(errors);
            showError('Error al crear el producto');
        }
    });
};
</script>

<template>
    <div class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
        <div
            class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 rounded-lg">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Agregar Producto Sin Stock</h3>
                <button @click="emits('close')" class="rounded-full p-2 hover:bg-muted/50">
                    <XIcon class="h-4 w-4" />
                </button>
            </div>

            <!-- Barcode Scanner Section -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-4">
                    <button @click="showScanner = !showScanner"
                        class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                        <ScanLineIcon class="h-4 w-4" />
                        {{ showScanner ? 'Ocultar Scanner' : 'Escanear Código' }}
                    </button>
                    <span v-if="scannedCode" class="text-sm text-muted-foreground">
                        Último escaneado: {{ scannedCode }}
                    </span>
                </div>

                <!-- Scanner View -->
                <div v-if="showScanner" class="border rounded-lg p-4">
                    <!-- Mobile-first layout -->
                    <div class="grid grid-rows-1 xl:grid-cols-2 gap-4">
                        <!-- Camera feed takes full width on mobile -->
                        <div class="w-full bg-black rounded-lg flex items-center justify-center">
                            <BarcodeReader @decode="(code: string) => {
                                scannedCode = code;
                                form.barcode = code
                                scannedCodes.push(code);
                            }" />
                        </div>

                        <!-- Scanned items below camera on mobile -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Códigos escaneados</h4>
                                <span class="text-sm text-muted-foreground">
                                    {{ scannedCodes.length }} en total
                                </span>
                            </div>
                            <div class="space-y-2 max-h-[200px] overflow-y-auto">
                                <div v-for="code in scannedCodes" :key="code"
                                    class="flex items-center justify-between p-2 bg-muted rounded-md">
                                    <span class="text-sm font-mono">{{ code }}</span>
                                    <button class="text-muted-foreground hover:text-destructive">
                                        <XIcon class="h-4 w-4" />
                                    </button>
                                </div>
                                <div v-if="scannedCodes.length === 0"
                                    class="text-sm text-muted-foreground text-center py-4">
                                    No hay códigos escaneados
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form @submit.prevent="handleSubmit" class="space-y-4">
                <div class="grid gap-4 items-center sm:grid-cols-2">
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Nombre del producto
                        </label>
                        <input v-model="form.name" type="text" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.name }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Código de barras
                        </label>
                        <input v-model="form.barcode" type="text" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.barcode }}</span>
                    </div>
                    <div class="col-span-2 space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Descripción
                        </label>
                        <textarea rows="3" v-model="form.description" required
                            class="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.description }}</span>
                    </div>
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Categoría
                        </label>
                        <select v-model="form.category_id" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                            <option value="">Selecciona una categoría</option>
                            <option v-for="category in categories" :key="category.id" :value="category.id">{{
                                category.name }}</option>
                        </select>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.category_id }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Local
                        </label>
                        <select v-model="form.local" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50">
                            <option value="">Selecciona un local</option>
                            <option v-for="local in page.props.locals as Local[]" :key="local.id" :value="local.id">{{
                                local.name }}</option>
                        </select>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.local }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Precio unitario
                        </label>
                        <input v-model="form.price" type="number" step="0.01" required
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.price }}</span>
                    </div>

                    <div class="space-y-2 items-center">
                        <input v-model="form.status" type="checkbox" id="available"
                            class="h-4 w-4 rounded border-input bg-background text-primary focus:outline-none focus:ring-1 focus:ring-ring" />
                        <label for="available"
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            ¿Dejar habilitado para venta?
                        </label>
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.status }}</span>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" @click="emits('close')"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                        Cancelar
                    </button>
                    <button type="submit" :disabled="form.processing"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                        Guardar
                    </button>
                </div>
            </form>
        </div>
    </div>
</template>
