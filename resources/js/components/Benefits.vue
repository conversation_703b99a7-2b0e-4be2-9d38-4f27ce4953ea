<script setup lang="ts">
import { ref, onMounted } from 'vue'

const benefits = [
    {
        id: 1,
        title: 'Reduce pérdidas por vencimiento',
        description: 'Identifica productos próximos a vencer y prioriza su venta. Evita pérdidas y mantén tu inventario fresco.',
        stat: '37%',
        statLabel: 'menos pérdidas'
    },
    {
        id: 2,
        title: 'Ahorra tiempo en tareas administrativas',
        description: 'Automatiza procesos manuales como conteos de inventario, pedidos a proveedores y actualización de precios.',
        stat: '15hs',
        statLabel: 'menos por semana'
    },
    {
        id: 3,
        title: 'Optimiza el espacio en almacén',
        description: 'Conoce exactamente qué tienes y dónde está. Organiza tu inventario de manera eficiente y ahorra espacio valioso.',
        stat: '25%',
        statLabel: 'más capacidad'
    }
]

const isVisible = ref(false)

onMounted(() => {
    const observer = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting) {
            isVisible.value = true
        }
    }, { threshold: 0.1 })

    const section = document.querySelector('#benefits')
    if (section) observer.observe(section)
})
</script>

<template>
    <section id="benefits" class="section-spacing bg-gray-50">
        <div class="container-custom">
            <div class="text-center max-w-3xl mx-auto mb-16">
                <h2 class="title-medium text-gray-900 mb-4">Beneficios reales para tu negocio</h2>
                <p class="text-lg text-gray-600">
                    Nuestros clientes experimentan mejoras significativas en su operación diaria, ahorrando tiempo y
                    dinero.
                </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                <div class="lg:col-span-5">
                    <div :class="['transition-all duration-1000 delay-300', isVisible ? 'opacity-100' : 'opacity-0 translate-y-8']"
                        class="relative rounded-xl overflow-hidden shadow-xl">
                        <img src="https://www.llanerosolidario.org/wp-content/uploads/2021/05/tienda-de-barrio-de-cualquier-ciudad.jpg"
                            alt="Dueño de local usando LocalWMS" class="w-full h-auto" />
                        <div class="absolute inset-0 bg-gradient-to-t from-gray-900/60 to-transparent flex items-end">
                            <div class="p-6 text-white">
                                <p class="font-medium text-lg">"Con LocalWMS reduje mis costos operativos
                                    significativamente"</p>
                                <p class="text-gray-200 mt-2">- María González, Minimarket "La Esquina"</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="lg:col-span-7">
                    <div class="space-y-8">
                        <div v-for="(benefit, index) in benefits" :key="benefit.id"
                            :class="['glass-card transition-all duration-700', isVisible ? 'opacity-100' : 'opacity-0 translate-y-8']"
                            :style="{ transitionDelay: `${300 + (index * 200)}ms` }">
                            <div class="flex flex-col md:flex-row gap-6">
                                <div class="flex-shrink-0 flex items-center">
                                    <div
                                        class="bg-primary rounded-lg h-14 w-14 flex items-center justify-center text-white text-xl font-bold">
                                        {{ benefit.stat }}
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-primary mb-1">{{ benefit.statLabel }}</p>
                                    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ benefit.title }}</h3>
                                    <p class="text-gray-600">{{ benefit.description }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>
