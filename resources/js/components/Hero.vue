<script setup lang="ts">
import { onMounted, ref } from 'vue'

const isVisible = ref(false)

onMounted(() => {
  isVisible.value = true
})
</script>

<template>
  <section class="relative overflow-hidden pt-32 pb-16 md:pt-40 md:pb-24">
    <div class="container-custom relative z-10">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div :class="['transition-all duration-1000 delay-300', isVisible ? 'opacity-100' : 'opacity-0 translate-y-8']">
          <h1 class="title-large text-gray-900 mb-6">
            Gestión de inventario <span class="text-primary">simple y efectiva</span> para tu negocio local
          </h1>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            LocalWMS es un sistema de gestión de almacén diseñado específicamente para comercios de barrio. Optimiza tu inventario, reduce costos y nunca más te quedes sin stock de productos esenciales.
          </p>
          <div class="flex flex-col sm:flex-row gap-4">
            <a href="#contact" class="btn-primary">Solicitar Demo Gratuita</a>
            <a href="#features" class="btn-secondary">Conocer Más</a>
          </div>
          <div class="mt-8 flex items-center">
            <div class="flex -space-x-2">
              <img src="https://images.pexels.com/photos/5453811/pexels-photo-5453811.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&dpr=2" alt="Usuario" class="w-10 h-10 rounded-full border-2 border-white" />
              <img src="https://images.pexels.com/photos/5314644/pexels-photo-5314644.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&dpr=2" alt="Usuario" class="w-10 h-10 rounded-full border-2 border-white" />
              <img src="https://images.pexels.com/photos/7648042/pexels-photo-7648042.jpeg?auto=compress&cs=tinysrgb&w=30&h=30&dpr=2" alt="Usuario" class="w-10 h-10 rounded-full border-2 border-white" />
            </div>
            <span class="ml-4 text-sm text-gray-600">Más de <span class="font-medium">500</span> comercios locales nos eligen</span>
          </div>
        </div>
        <div :class="['relative transition-all duration-1000 delay-500', isVisible ? 'opacity-100' : 'opacity-0 translate-y-8']">
          <div class="relative rounded-xl overflow-hidden shadow-2xl">
            <img src="https://images.pexels.com/photos/6169658/pexels-photo-6169658.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Sistema WMS en uso" class="w-full h-auto rounded-xl" />
            <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent"></div>
          </div>
          <div class="absolute -bottom-6 -left-6 bg-white rounded-lg shadow-lg p-4 max-w-xs animate-pulse-slow">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="rounded-full bg-cyan-100 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900">¡Stock actualizado!</p>
                <p class="text-xs text-gray-500">Recibiste 24 nuevos productos</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Decorative background elements -->
    <div class="absolute top-0 right-0 -translate-y-1/4 translate-x-1/4 transform">
      <div class="w-96 h-96 bg-secondary-100 rounded-full opacity-70 blur-3xl"></div>
    </div>
    <div class="absolute bottom-0 left-0 translate-y-1/4 -translate-x-1/4 transform">
      <div class="w-96 h-96 bg-accent-100 rounded-full opacity-70 blur-3xl"></div>
    </div>
  </section>
</template>
