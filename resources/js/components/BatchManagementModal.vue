<script setup lang="ts">
import { ScanLineIcon, XIcon } from 'lucide-vue-next';
import { onMounted, ref } from 'vue';
import { useForm } from '@inertiajs/vue3';
import BarcodeReader from './BarcodeReader.vue';
import { useRadixToast } from '@/composables/useRadixToast';

interface Batch {
    id: number;
    barcode: string | null;
    expiration_date: string | null;
    quantity: number;
}

const props = defineProps<{
    productId?: number;
}>();

const showScanner = ref(false);
const scannedCode = ref('');
const scannedCodes = ref<string[]>([]);

const form = useForm({
    product_id: props.productId || '',
    barcode: '',
    expiration_date: '',
    quantity: '0',
});

const { showSuccess, showError } = useRadixToast();

onMounted(() => {
    if (props.productId) {
        form.product_id = props.productId;
    }
});

const emits = defineEmits(['close', 'batch-created']);

const handleSubmit = () => {
    if (!form.product_id) {
        showError('No se ha seleccionado un producto');
        return;
    }

    form.post(route('create-batch-stock-entry'), {
        preserveScroll: true,
        onSuccess: (response) => {
            showSuccess('Lote creado correctamente');
            // Emitir evento con el lote creado
            if (response?.props?.batch) {
                emits('batch-created', response.props.batch);
            } else {
                emits('close');
            }
        },
        onError: (errors) => {
            console.error(errors);
            showError('Error al crear el lote');
        }
    });
};
</script>

<template>
    <div class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
        <div
            class="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 rounded-lg">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold">Crear Nuevo Lote</h3>
                <button @click="emits('close')" class="rounded-full p-2 hover:bg-muted/50">
                    <XIcon class="h-4 w-4" />
                </button>
            </div>

            <!-- Barcode Scanner Section -->
            <div class="mb-6">
                <div class="flex items-center gap-2 mb-4">
                    <button @click="showScanner = !showScanner"
                        class="inline-flex items-center justify-center gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm hover:bg-accent hover:text-accent-foreground">
                        <ScanLineIcon class="h-4 w-4" />
                        {{ showScanner ? 'Ocultar Scanner' : 'Escanear Código' }}
                    </button>
                    <span v-if="scannedCode" class="text-sm text-muted-foreground">
                        Último escaneado: {{ scannedCode }}
                    </span>
                </div>

                <!-- Scanner View -->
                <div v-if="showScanner" class="border rounded-lg p-4">
                    <!-- Mobile-first layout -->
                    <div class="grid grid-rows-1 xl:grid-cols-2 gap-4">
                        <!-- Camera feed takes full width on mobile -->
                        <div class="w-full bg-black rounded-lg flex items-center justify-center">
                            <BarcodeReader @decode="(code: string) => {
                                scannedCode = code;
                                form.barcode = code
                                scannedCodes.push(code);
                            }" />
                        </div>

                        <!-- Scanned items below camera on mobile -->
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <h4 class="font-medium">Códigos escaneados</h4>
                                <span class="text-sm text-muted-foreground">
                                    {{ scannedCodes.length }} en total
                                </span>
                            </div>
                            <div class="space-y-2 max-h-[200px] overflow-y-auto">
                                <div v-for="code in scannedCodes" :key="code"
                                    class="flex items-center justify-between p-2 bg-muted rounded-md">
                                    <span class="text-sm font-mono">{{ code }}</span>
                                    <button class="text-muted-foreground hover:text-destructive">
                                        <XIcon class="h-4 w-4" />
                                    </button>
                                </div>
                                <div v-if="scannedCodes.length === 0"
                                    class="text-sm text-muted-foreground text-center py-4">
                                    No hay códigos escaneados
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form @submit.prevent="handleSubmit" class="space-y-4">
                <div class="grid gap-4 items-center sm:grid-cols-2">
                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Código de barras (opcional)
                        </label>
                        <input v-model="form.barcode" type="text"
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.barcode }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Fecha de vencimiento
                        </label>
                        <input v-model="form.expiration_date" type="date"
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.expiration_date }}</span>
                    </div>

                    <div class="space-y-2">
                        <label
                            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Cantidad inicial
                        </label>
                        <input v-model="form.quantity" type="number" step="1" min="0"
                            class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                        <span class="text-sm text-red-600 dark:text-red-500">{{ form.errors.quantity }}</span>
                    </div>
                </div>

                <div class="flex justify-end space-x-2">
                    <button type="button" @click="emits('close')"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                        Cancelar
                    </button>
                    <button type="submit" :disabled="form.processing"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                        Crear Lote
                    </button>
                </div>
            </form>
        </div>
    </div>
</template>
