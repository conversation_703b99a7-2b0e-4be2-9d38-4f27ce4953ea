<script setup lang="ts">
import ToastManager from '@/components/ToastManager.vue';
import AppLayout from '@/layouts/app/AppSidebarLayout.vue';
import type { BreadcrumbItemType } from '@/types';
import { ToastProvider, ToastViewport } from 'radix-vue';

interface Props {
    breadcrumbs?: BreadcrumbItemType[];
}

withDefaults(defineProps<Props>(), {
    breadcrumbs: () => [],
});
</script>

<template>
    <ToastProvider>
        <AppLayout :breadcrumbs="breadcrumbs">
            <slot />
        </AppLayout>
        <ToastManager />
        <ToastViewport class="fixed bottom-4 right-4 w-96 z-50" />
    </ToastProvider>
</template>
