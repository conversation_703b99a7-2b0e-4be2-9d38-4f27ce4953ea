<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue'
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'
import { Link } from '@inertiajs/vue3'

const navigation = [
    { name: 'Características', href: '#features' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '#benefits' },
    { name: 'FAQ', href: '#faq' }
]

const scrolled = ref(false)

const checkScroll = () => {
    scrolled.value = window.scrollY > 10
}

onMounted(() => {
    window.addEventListener('scroll', checkScroll)
    checkScroll()
})

onUnmounted(() => {
    window.removeEventListener('scroll', checkScroll)
})
</script>

<template>
    <Disclosure as="nav" :class="[
        'fixed w-full z-50 transition-all duration-300',
        scrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'
    ]">
        <div class="container-custom">
            <div class="relative grid  grid-flow-col grid-cols-3 sm:flex items-center justify-between h-16">
                <div class="absolute inset-y-0 left-0 flex items-center sm:hidden">
                    <!-- Mobile menu button-->
                    <DisclosureButton
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary">
                        <span class="sr-only">Abrir menú principal</span>
                        <Bars3Icon v-if="!$slots.default || !$slots.default()" class="block h-6 w-6"
                            aria-hidden="true" />
                        <XMarkIcon v-else class="block h-6 w-6" aria-hidden="true" />
                    </DisclosureButton>
                </div>
                <div class="flex items-center justify-center sm:items-stretch sm:justify-start">
                    <div class="">
                        <span class="text-xl font-bold">MiAlmacen</span>
                    </div>
                    <div class="hidden sm:block sm:ml-6">
                        <div class="flex space-x-4">
                            <a v-for="item in navigation" :key="item.name" :href="item.href"
                                class="px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-primary transition-colors duration-200">
                                {{ item.name }}
                            </a>
                        </div>
                    </div>
                </div>
                <div
                    class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0 space-x-4">
                    <Link href="login"
                        class="px-4 py-2 rounded-md text-sm font-medium hover:text-white hover:bg-primary transition-colors duration-200">
                    Iniciar Sesión
                    </Link>
                    <a href="#contact" class="btn-primary">Solicitar Demo</a>
                </div>
            </div>
        </div>

        <DisclosurePanel class="sm:hidden bg-white shadow-lg">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a v-for="item in navigation" :key="item.name" :href="item.href"
                    class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-gray-600 hover:bg-gray-50">
                    {{ item.name }}
                </a>
            </div>
        </DisclosurePanel>
    </Disclosure>
</template>
