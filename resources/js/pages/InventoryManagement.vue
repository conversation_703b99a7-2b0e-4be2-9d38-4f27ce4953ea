<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { ref } from 'vue';
import {
    PlusIcon,
    UploadIcon
} from 'lucide-vue-next';

import AddProductForm from '@/components/AddProductForm.vue';
import ImportProductsModal from '@/components/ImportProductsModal.vue';
import ProductListTable from '@/components/ProductListTable.vue';

const showForm = ref(false);
const showImportModal = ref(false);

const closeImportModal = () => {
    showImportModal.value = false;
}

const closeAddProductForm = () => {
    showForm.value = false;
}

</script>

<template>
    <AppLayout :breadcrumbs="[{ href: '/inventory', title: 'Gestión de inventario' }]">
        <div class="grid gap-6 p-6">
            <!-- Header with Actions -->
            <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <h2 class="text-2xl font-bold tracking-tight">Product Management</h2>
                <div class="flex gap-2">
                    <button @click="showForm = true"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                        <PlusIcon class="mr-2 h-4 w-4" />
                        Add Product
                    </button>
                    <button @click="showImportModal = true"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                        <UploadIcon class="mr-2 h-4 w-4" />
                        Import Products
                    </button>
                </div>
            </div>

            <!-- Add/Edit Product Form -->
            <AddProductForm @close="closeAddProductForm" v-if="showForm" />

            <!-- Import Modal -->
            <ImportProductsModal v-if="showImportModal" @close="closeImportModal" />

            <!-- Products List -->
            <ProductListTable />
        </div>
    </AppLayout>
</template>
