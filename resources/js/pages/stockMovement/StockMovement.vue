<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { ref } from 'vue';
import { PlusIcon } from 'lucide-vue-next';
import StockEntryForm from '@/components/StockEntryForm.vue';

const showStockEntryForm = ref(false);

const closeStockEntryForm = () => {
    showStockEntryForm.value = false;
}
</script>

<template>
    <AppLayout :breadcrumbs="[{ href: '/stock-movement', title: 'Gestión de Inventario' }]">
        <div class="grid gap-6 p-6">
            <!-- Header with Actions -->
            <div class="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <h2 class="text-2xl font-bold tracking-tight">Gestión de Inventario</h2>
                <div class="flex gap-2">
                    <button @click="showStockEntryForm = true"
                        class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                        <PlusIcon class="mr-2 h-4 w-4" />
                        Ingresar Stock
                    </button>
                </div>
            </div>

            <!-- Stock Entry Form -->
            <StockEntryForm @close="closeStockEntryForm" v-if="showStockEntryForm" />

            <!-- Stock Movements List (Pendiente de implementar) -->
            <div class="rounded-lg border bg-card shadow-sm p-6" v-if="!showStockEntryForm">
                <h3 class="text-lg font-semibold mb-4">Movimientos de Stock Recientes</h3>
                <p class="text-muted-foreground">Aquí se mostrarán los movimientos de stock recientes.</p>
            </div>
        </div>
    </AppLayout>
</template>
