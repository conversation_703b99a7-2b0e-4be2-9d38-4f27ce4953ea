<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import {
    PackageIcon,
    UsersIcon,
    AlertCircleIcon,
    TruckIcon
} from 'lucide-vue-next';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

const stats = [
    {
        title: 'Total Products',
        value: '1,248',
        change: '+12.5% from last month',
        percentage: '+12.5%',
        icon: PackageIcon,
        iconColor: 'text-primary',
        changeColor: 'bg-success/10 text-success'
    },
    {
        title: 'Low Stock Items',
        value: '42',
        change: '-8.2% from last month',
        percentage: '-8.2%',
        icon: AlertCircleIcon,
        iconColor: 'text-warning',
        changeColor: 'bg-success/10 text-success'
    },
    {
        title: 'Active Suppliers',
        value: '38',
        change: '+2.7% from last month',
        percentage: '+2.7%',
        icon: UsersIcon,
        iconColor: 'text-accent',
        changeColor: 'bg-success/10 text-success'
    },
    {
        title: 'Pending Orders',
        value: '12',
        change: '+4.3% from last month',
        percentage: '+4.3%',
        icon: TruckIcon,
        iconColor: 'text-secondary',
        changeColor: 'bg-destructive/10 text-destructive'
    }
];

const inventoryChartOptions = {
    chart: {
        toolbar: {
            show: false,
        },
        zoom: {
            enabled: false,
        },
    },
    colors: ['#0F172A', '#10B981'],
    dataLabels: {
        enabled: false,
    },
    stroke: {
        width: 3,
        curve: 'smooth',
    },
    xaxis: {
        categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        axisBorder: {
            show: false,
        },
        axisTicks: {
            show: false,
        },
    },
    yaxis: {
        labels: {
            formatter: (value: number) => `${value}`,
        },
    },
    legend: {
        position: 'top',
        horizontalAlign: 'right',
    },
    grid: {
        borderColor: '#f1f1f1',
    },
    tooltip: {
        theme: 'light',
    },
    fill: {
        type: 'gradient',
        gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.1,
            inverseColors: false,
            opacityFrom: 0.7,
            opacityTo: 0.2,
        },
    },
};

const inventoryChartSeries = [
    {
        name: 'Inbound',
        data: [28, 45, 35, 50, 32, 55, 23],
    },
    {
        name: 'Outbound',
        data: [14, 25, 20, 25, 12, 20, 15],
    },
];

const categoryChartOptions = {
    chart: {
        type: 'donut',
    },
    colors: ['#0F172A', '#10B981', '#F59E0B', '#EF4444', '#6366F1'],
    legend: {
        position: 'bottom',
    },
    labels: ['Electronics', 'Groceries', 'Apparel', 'Home Goods', 'Other'],
    dataLabels: {
        enabled: false,
    },
    plotOptions: {
        pie: {
            donut: {
                size: '65%',
            },
        },
    },
};

const categoryChartSeries = [42, 23, 15, 12, 8];

const recentActivity = [
    {
        id: 1,
        event: 'Stock Received',
        product: 'Samsung TV 55"',
        quantity: 15,
        status: 'Completed',
        date: 'Today, 10:30 AM'
    },
    {
        id: 2,
        event: 'Order Shipped',
        product: 'Apple iPhone 13',
        quantity: 8,
        status: 'Shipped',
        date: 'Today, 9:15 AM'
    },
    {
        id: 3,
        event: 'Low Stock Alert',
        product: 'Dell XPS Laptop',
        quantity: 2,
        status: 'Alert',
        date: 'Yesterday, 4:45 PM'
    },
    {
        id: 4,
        event: 'Return Processed',
        product: 'Sony Headphones',
        quantity: 1,
        status: 'Returned',
        date: 'Yesterday, 2:30 PM'
    },
    {
        id: 5,
        event: 'Inventory Count',
        product: 'Grocery Items',
        quantity: 120,
        status: 'Completed',
        date: '2 days ago'
    },
];

const activityStatusClass = (status: string) => {
    switch (status) {
        case 'Completed':
            return 'bg-success/10 text-success';
        case 'Shipped':
            return 'bg-primary/10 text-primary';
        case 'Alert':
            return 'bg-destructive/10 text-destructive';
        case 'Returned':
            return 'bg-warning/10 text-warning';
        default:
            return 'bg-muted text-muted-foreground';
    }
};
</script>

<template>

    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
            <h2 class="text-2xl font-bold tracking-tight">Resumen</h2>
            <div class="grid auto-rows-min gap-4 md:grid-cols-4">
                <div v-for="stat in stats" :key="stat.title"
                    class="rounded-lg border bg-card p-6 shadow-sm transition-all hover:shadow-md">
                    <div class="flex items-center gap-2">
                        <component :is="stat.icon" class="h-5 w-5" :class="stat.iconColor" />
                        <span class="text-sm font-medium">{{ stat.title }}</span>
                    </div>
                    <div class="mt-3 flex items-end justify-between">
                        <div>
                            <p class="text-2xl font-bold">{{ stat.value }}</p>
                            <p class="text-xs text-muted-foreground">
                                {{ stat.change }}
                            </p>
                        </div>
                        <div class="rounded-md px-2 py-1 text-xs font-medium" :class="stat.changeColor">
                            {{ stat.percentage }}
                        </div>
                    </div>
                </div>
            </div>
            <section class="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
                <!-- Revenue Chart - Wider -->
                <div class="col-span-4 rounded-lg border bg-card p-6 shadow-sm">
                    <div class="flex items-center justify-between">
                        <h3 class="font-semibold">Movimientos de stock</h3>
                        <div class="flex items-center gap-2">
                            <select class="h-8 rounded-md border border-input bg-background px-2 py-1 text-xs">
                                <option value="7days">Last 7 days</option>
                                <option value="30days">Last 30 days</option>
                                <option value="90days">Last 90 days</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4 h-[300px]">
                        <apexchart type="line" height="300" :options="inventoryChartOptions"
                            :series="inventoryChartSeries"></apexchart>
                    </div>
                </div>

                <!-- Products Chart - Narrower -->
                <div class="col-span-3 rounded-lg border bg-card p-6 shadow-sm">
                    <div class="flex flex-col space-y-2">
                        <h3 class="font-semibold">Mas vendidos</h3>
                        <p class="text-xs text-muted-foreground">Productos mas vendidos</p>
                    </div>
                    <div class="mt-4 h-[300px]">
                        <apexchart type="donut" height="300" :options="categoryChartOptions"
                            :series="categoryChartSeries"></apexchart>
                    </div>
                </div>
            </section>

            <section class="rounded-lg border bg-card p-6 shadow-sm">
                <h3 class="font-semibold mb-4">Productos Faltos de stock</h3>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse">
                        <thead>
                            <tr class="border-b">
                                <th class="py-3 text-left text-sm font-medium text-muted-foreground">Event</th>
                                <th class="py-3 text-left text-sm font-medium text-muted-foreground">Product</th>
                                <th class="py-3 text-left text-sm font-medium text-muted-foreground">Quantity</th>
                                <th class="py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                                <th class="py-3 text-left text-sm font-medium text-muted-foreground">Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="activity in recentActivity" :key="activity.id"
                                class="border-b hover:bg-muted/50">
                                <td class="py-3 text-sm">{{ activity.event }}</td>
                                <td class="py-3 text-sm">{{ activity.product }}</td>
                                <td class="py-3 text-sm">{{ activity.quantity }}</td>
                                <td class="py-3 text-sm">
                                    <span class="inline-flex rounded-full px-2 py-1 text-xs font-medium"
                                        :class="activityStatusClass(activity.status)">
                                        {{ activity.status }}
                                    </span>
                                </td>
                                <td class="py-3 text-sm text-muted-foreground">{{ activity.date }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </AppLayout>
</template>
