import Echo from 'laravel-echo';

import Pusher from 'pusher-js';
window.Pusher = Pusher;

console.log('Configurando Echo con:', {
    key: import.meta.env.VITE_REVERB_APP_KEY,
    host: import.meta.env.VITE_REVERB_HOST,
    port: import.meta.env.VITE_REVERB_PORT,
    scheme: import.meta.env.VITE_REVERB_SCHEME
});

window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT ?? 80,
    wssPort: import.meta.env.VITE_REVERB_PORT ?? 443,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});

// Eventos de conexión para debugging
window.Echo.connector.pusher.connection.bind('connected', () => {
    console.log('✅ WebSocket conectado exitosamente');
});

window.Echo.connector.pusher.connection.bind('disconnected', () => {
    console.log('❌ WebSocket desconectado');
});

window.Echo.connector.pusher.connection.bind('error', (error) => {
    console.error('❌ Error de WebSocket:', error);
});

// Debug: Capturar TODOS los eventos
window.Echo.connector.pusher.bind_global((eventName, data) => {
    console.log('🔍 Evento global capturado:', eventName, data);

    // Si es el evento que esperamos, parsearlo manualmente
    if (eventName === 'product-created') {
        try {
            const parsedData = typeof data === 'string' ? JSON.parse(data) : data;
            console.log('✅ Producto creado (parseado manualmente):', parsedData);
        } catch (e) {
            console.error('❌ Error parseando datos del producto:', e);
        }
    }
});

// Canal para productos creados externamente
console.log('📡 Configurando canal: product-created-external');
window.Echo.channel('product-created-external')
    .listen('product-created', (e) => {
        console.log('✅ Producto creado externamente (datos raw):', e);

        try {
            // Intentar parsear los datos si vienen como string
            let productData = e;
            if (typeof e === 'string') {
                productData = JSON.parse(e);
            }

            console.log('✅ Producto creado externamente (parseado):', productData);

            // Aquí puedes agregar la lógica para manejar el producto creado
            if (productData.product) {
                console.log('📦 Nuevo producto:', productData.product.name);
                // Ejemplo: mostrar notificación, actualizar UI, etc.
            }
        } catch (error) {
            console.error('❌ Error parseando datos del producto:', error);
        }
    })
    .error((error) => {
        console.error('❌ Error en canal product-created-external:', error);
    });

// Canal para base de datos ocupada
console.log('📡 Configurando canal: external-database-busy');
window.Echo.channel('external-database-busy')
    .listen('external-database-busy', (e) => {
        console.log('⚠️ Base de datos externa ocupada:', e);
    })
    .error((error) => {
        console.error('❌ Error en canal external-database-busy:', error);
    });

// Canal para productos no encontrados
console.log('📡 Configurando canal: product-search-external');
window.Echo.channel('product-search-external')
    .listen('product-not-found', (e) => {
        console.log('❌ Producto no encontrado en base de datos externa:', e);
    })
    .error((error) => {
        console.error('❌ Error en canal product-search-external:', error);
    });

// Debug adicional: Verificar que los canales se estén suscribiendo correctamente
setTimeout(() => {
    console.log('📋 Canales suscritos:', Object.keys(window.Echo.connector.pusher.channels.channels));

    // Verificar cada canal
    Object.keys(window.Echo.connector.pusher.channels.channels).forEach(channelName => {
        const channel = window.Echo.connector.pusher.channels.channels[channelName];
        console.log(`📡 Canal ${channelName}:`, {
            subscribed: channel.subscribed,
            callbacks: Object.keys(channel.callbacks || {}),
            globalCallbacks: Object.keys(channel.global_callbacks || {})
        });
    });
}, 2000);
