<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // create permissions
        Permission::create(['name' => 'vender']);
        Permission::create(['name' => 'anadir stock']);
        Permission::create(['name' => 'movimientos de stock']);
        Permission::create(['name' => 'administrar local']);
        Permission::create(['name' => 'administrar clientes']);



        // update cache to know about the newly created permissions (required if using WithoutModelEvents in seeders)
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();


        // create roles and assign created permissions

        // this can be done as separate statements
        $seller = Role::create(['name' => 'vendedor']);
        $seller->givePermissionTo('vender');
        $seller->givePermissionTo('anadir stock');

        $client = Role::create(['name' => 'cliente']);
        $client->givePermissionTo('movimientos de stock');
        $client->givePermissionTo('administrar local');

        $ceo = Role::create(['name' => 'CEO']);
        $ceo->givePermissionTo('administrar clientes');
        $ceo->givePermissionTo('administrar clientes');


    }
}
