<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class LocalProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();

        $products->each(function ($product) {
            $expirationDate = new \DateTime();

            $attributes = [
                'price' => rand(1, 10000),
                'stock' => rand(1, 10),
                'min_stock' => 3,
                'max_stock' => rand(10, 20),
                'expiration_date' => $expirationDate->add(new \DateInterval('P'.rand(30, 60).'D')),
                'status' => true,
                'created_at' => new \DateTime(),
                'updated_at' => new \DateTime(),
            ];

            $product->locals()->attach(rand(1, 5), $attributes);
        });
    }
}
