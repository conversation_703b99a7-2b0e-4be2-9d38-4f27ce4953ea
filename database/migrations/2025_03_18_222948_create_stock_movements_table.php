<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_movements', function (Blueprint $table) {
            $table->id();
            $table->integer('quantity');
            $table->text('movement_reason');
            $table->foreignId('local_products_id')->references('id')->on('local_products');
            $table->foreignId('stock_movement_types_id')->references('id')->on('stock_movement_types');
            $table->foreignId('order_id')->refrences('id')->on('orders');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_movements');
    }
};
