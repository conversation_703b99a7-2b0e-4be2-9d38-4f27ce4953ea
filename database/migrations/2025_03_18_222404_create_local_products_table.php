<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class() extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('local_products', function (Blueprint $table) {
            $table->id();
            $table->double('price');
            $table->integer('stock');
            $table->integer('min_stock');
            $table->integer('max_stock');
            $table->dateTime('expiration_date');
            $table->boolean('status');
            $table->bigInteger('local_id');
            $table->foreign('local_id')->references('id')->on('locals');
            $table->foreignId('product_id')->references('id')->on('products');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('local_products');
    }
};
