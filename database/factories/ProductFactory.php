<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->word(),
            'barcode' => fake()->unique()->ean13(),
            'description' => fake()->sentence(),
            'image' => fake()->imageUrl(640,480, 'technics'),
            'category_id' => fake()->numberBetween(1,10)
        ];
    }
}
