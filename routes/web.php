<?php

use App\Http\Controllers\Category\CategoryController;
use App\Http\Controllers\Products\ProductController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::post('create-product', [ProductController::class, 'createProduct'])->middleware(['auth', 'verified'])->name('create-product');
Route::get('categories', [CategoryController::class, 'getCategories'])->middleware(['auth', 'verified']);

Route::get('products', [App\Http\Controllers\Products\ProductController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('products');

Route::get('stock-movement', [App\Http\Controllers\StockMovement\StockMovementController::class, 'index'])->middleware(['auth', 'verified'])->name('stock-movement');
Route::get('api/products/barcode/{barcode}', [App\Http\Controllers\Products\ProductController::class, 'findByBarcode']);

Route::post('create-batch-stock-entry', [App\Http\Controllers\Batch\BatchController::class, 'createBatchAtStockEntry'])->middleware(['auth', 'verified'])->name('create-batch-stock-entry');
Route::post('add-product-to-local', [App\Http\Controllers\LocalProduct\LocalProductController::class, 'addProductToLocal'])->middleware(['auth', 'verified'])->name('add-product-to-local');
Route::post('stock-entry', [App\Http\Controllers\StockMovement\StockMovementController::class, 'store'])->middleware(['auth', 'verified'])->name('stock.entry');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
